BASE_URL=https://mobile-dev-gurkha-id.asean-accesstrade.net
CLIENT_ID='koc-mobile-app'
SUPER_POINT_PAGE_LINK='https://superpoint-staging.accesstrade.global/autologin.php?secret='
SHOW_DEBUG_TOOLS=true

# Facebook Sign In
FACEBOOK_APP_ID=493145729987242
FACEBOOK_APP_SECRET=********************************
FACEBOOK_CLIENT_TOKEN=********************************
FACEBOOK_REDIRECT_URI=https://staging-at-koc.firebaseapp.com/__/auth/handler

# Google OAuth Credentials
# Common Project Info (Reference for GoogleService-Info.plist & google-services.json)
GOOGLE_PROJECT_ID=at-koc-app
GOOGLE_PROJECT_NUMBER=230569016081
GOOGLE_API_KEY=AIzaSyAAwt0GMwKY9T5kOmDm_4NFt0D79-6cNS0


# iOS
GOOGLE_CLIENT_ID_IOS=230569016081-oq5ef9rgt5iba1rjo118djfq291urkj7.apps.googleusercontent.com
GOOGLE_REVERSED_CLIENT_ID_IOS=com.googleusercontent.apps.230569016081-oq5ef9rgt5iba1rjo118djfq291urkj7

# Android
GOOGLE_CLIENT_ID_ANDROID=230569016081-lpv8i3t2m9qt0o57hdractk6f2it4msg.apps.googleusercontent.com

# Web Client ID
# -----------
# Often used for Android's google_web_client_id or server_client_id string resource
GOOGLE_CLIENT_ID_WEB=185534151793-cmagnd13iigca9sl6rbipj1d0pev1e4r.apps.googleusercontent.com
GOOGLE_REDIRECT_URI_WEB=https://mobile-dev-gurkha-id.asean-accesstrade.net/auth/google/callback

# Android Signing Configuration
KEY_ALIAS=koc-mobile
KEY_PASSWORD='KxZpX70h#8cf'
STORE_FILE=release.keystore
STORE_PASSWORD='KxZpX70h#8cf'