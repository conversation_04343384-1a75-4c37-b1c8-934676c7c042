flutter_launcher_icons:
  # Use app-icon.png as the source image for all platforms
  image_path: "assets/images/app-icon.png"
  
  # Generate icons for Android
  android: true
  
  # Generate icons for iOS
  ios: true
  
  # Generate icons for Web
  web:
    generate: true
    image_path: "assets/images/app-icon.png"
    background_color: "#FFB522"
    theme_color: "#FFB522"
  
  # Generate icons for Windows
  windows:
    generate: true
    image_path: "assets/images/app-icon.png"
    icon_size: 48 # min:48, max:256, default: 48
  
  # Generate icons for macOS
  macos:
    generate: true
    image_path: "assets/images/app-icon.png"
  
  # Generate icons for Linux
  linux:
    generate: true
    image_path: "assets/images/app-icon.png"
  
  # Remove alpha channel for better compatibility
  remove_alpha_ios: true
  
  # Adaptive icon for Android (API 26+)
  adaptive_icon_background: "#FFB522"
  adaptive_icon_foreground: "assets/images/app-icon.png"
  
  # Minimum SDK version for adaptive icons
  min_sdk_android: 21
