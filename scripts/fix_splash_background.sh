#!/bin/bash

# <PERSON>ript to fix Android splash screen background to use solid color instead of bitmap
# This script should be run after 'dart run flutter_native_splash:create'

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}Fixing Android splash screen background...${NC}"

# Define the correct launch_background.xml content with solid color
LAUNCH_BACKGROUND_CONTENT='<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#FFB522"/>
        </shape>
    </item>
    <item>
        <bitmap android:gravity="center" android:src="@drawable/splash"/>
    </item>
</layer-list>'

# Fix launch_background.xml in drawable folder
echo -e "${YELLOW}Updating android/app/src/main/res/drawable/launch_background.xml...${NC}"
echo "$LAUNCH_BACKGROUND_CONTENT" > "android/app/src/main/res/drawable/launch_background.xml"

# Fix launch_background.xml in drawable-v21 folder
echo -e "${YELLOW}Updating android/app/src/main/res/drawable-v21/launch_background.xml...${NC}"
echo "$LAUNCH_BACKGROUND_CONTENT" > "android/app/src/main/res/drawable-v21/launch_background.xml"

# Remove unnecessary background.png files
echo -e "${YELLOW}Removing unnecessary background.png files...${NC}"
rm -f "android/app/src/main/res/drawable/background.png"
rm -f "android/app/src/main/res/drawable-v21/background.png"

echo -e "${GREEN}✅ Android splash screen background fixed!${NC}"
echo -e "${GREEN}Background is now solid color #FFB522 instead of bitmap.${NC}"
echo ""
echo -e "${YELLOW}Note: Run this script after every 'dart run flutter_native_splash:create' command.${NC}"
