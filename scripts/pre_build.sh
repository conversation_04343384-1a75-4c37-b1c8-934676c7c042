#!/bin/bash

# <PERSON><PERSON>t to run before building the app
# This script:
# 1. Checks if .env file exists, if not, creates it from .env.example
# 2. Creates necessary directories and placeholder files if they don't exist
# 3. Updates Android and iOS resources with values from .env for Facebook and Google

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}Running pre-build script...${NC}"
echo "=============================="
echo ""

# Function to load .env file safely (handles Windows line endings)
load_env_file() {
  if [ -f ".env" ]; then
    # Remove Windows line endings and source the file
    sed 's/\r$//' .env > .env.tmp
    source .env.tmp
    rm -f .env.tmp
    return 0
  else
    echo -e "${RED}ERROR: .env file not found!${NC}"
    return 1
  fi
}

# Function to check for missing environment variables
check_env_variables() {
  echo -e "${YELLOW}Checking for missing environment variables...${NC}"

  # Load .env file safely
  if ! load_env_file; then
    return 1
  fi

  # Define required variables from .env.example
  required_vars=(
    "BASE_URL"
    "CLIENT_ID"
    "SUPER_POINT_PAGE_LINK"
    "FACEBOOK_APP_ID"
    "FACEBOOK_APP_SECRET"
    "FACEBOOK_CLIENT_TOKEN"
    "FACEBOOK_REDIRECT_URI"
    "GOOGLE_REVERSED_CLIENT_ID_IOS"
    "GOOGLE_CLIENT_ID_WEB"
    "GOOGLE_REDIRECT_URI_WEB"
    "KEY_ALIAS"
    "KEY_PASSWORD"
    "STORE_FILE"
    "STORE_PASSWORD"
  )

  # Define placeholder values that indicate the variable needs to be set
  placeholder_patterns=(
    "YOUR_"
    "https://YOUR_DOMAIN"
    "com.googleusercontent.apps.YOUR_"
  )

  missing_vars=()
  placeholder_vars=()

  # Check each required variable
  for var in "${required_vars[@]}"; do
    value="${!var}"

    if [ -z "$value" ]; then
      missing_vars+=("$var")
    else
      # Check if value contains placeholder patterns
      for pattern in "${placeholder_patterns[@]}"; do
        if [[ "$value" == *"$pattern"* ]]; then
          placeholder_vars+=("$var")
          break
        fi
      done
    fi
  done

  # Report missing variables
  if [ ${#missing_vars[@]} -gt 0 ]; then
    echo -e "${RED}WARNING: The following required variables are missing from .env:${NC}"
    for var in "${missing_vars[@]}"; do
      echo -e "${RED}  - $var${NC}"
    done
    echo ""
  fi

  # Report placeholder variables
  if [ ${#placeholder_vars[@]} -gt 0 ]; then
    echo -e "${YELLOW}WARNING: The following variables still contain placeholder values:${NC}"
    for var in "${placeholder_vars[@]}"; do
      value="${!var}"
      echo -e "${YELLOW}  - $var = $value${NC}"
    done
    echo ""
  fi

  # Summary
  if [ ${#missing_vars[@]} -eq 0 ] && [ ${#placeholder_vars[@]} -eq 0 ]; then
    echo -e "${GREEN}All required environment variables are properly configured.${NC}"
  else
    echo -e "${YELLOW}Please update the .env file with actual values before building for production.${NC}"
  fi
  echo ""
}

# Check if .env file exists
if [ ! -f ".env" ]; then
  echo -e "${YELLOW}No .env file found. Creating from .env.example...${NC}"
  cp .env.example .env
  echo -e "${GREEN}Created .env file. Please edit it with your actual credentials.${NC}"
  echo ""
else
  echo -e "${GREEN}.env file already exists.${NC}"
fi

# Check environment variables
check_env_variables

# Create directory for Android resources if it doesn't exist
ANDROID_DIR="android/app/src/main/res/values"
if [ ! -d "$ANDROID_DIR" ]; then
  echo -e "${YELLOW}Creating Android resources directory...${NC}"
  mkdir -p "$ANDROID_DIR"
fi

# Check if strings.xml exists, if not create it with placeholders
if [ ! -f "$ANDROID_DIR/strings.xml" ]; then
  echo -e "${YELLOW}Creating placeholder strings.xml...${NC}"
  cat > "$ANDROID_DIR/strings.xml" << EOF
<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">KOC App</string>
    <string name="google_web_client_id">YOUR_GOOGLE_WEB_CLIENT_ID.apps.googleusercontent.com</string>
    <string name="facebook_app_id">YOUR_FACEBOOK_APP_ID</string>
    <string name="fb_login_protocol_scheme">fbYOUR_FACEBOOK_APP_ID</string>
    <string name="facebook_client_token">YOUR_FACEBOOK_CLIENT_TOKEN</string>
</resources>
EOF
  echo -e "${GREEN}Created placeholder strings.xml${NC}"
else
  echo -e "${GREEN}strings.xml already exists.${NC}"
fi

# Function to update Android resources
update_android_resources() {
  echo -e "${YELLOW}Updating Android resources...${NC}"
  
  # Load .env file safely
  if ! load_env_file; then
    return 1
  fi
  
  local strings_file="android/app/src/main/res/values/strings.xml"
  
  if [ ! -f "$strings_file" ]; then
    echo -e "${RED}ERROR: strings.xml file not found at $strings_file${NC}"
    return 1
  fi
  
  local facebook_updated=false
  local google_updated=false
  
  # Update Facebook App ID
  if [ -n "$FACEBOOK_APP_ID" ]; then
    sed -i.bak "s/<string name=\"facebook_app_id\">[^<]*<\/string>/<string name=\"facebook_app_id\">$FACEBOOK_APP_ID<\/string>/g" "$strings_file"
    sed -i.bak "s/<string name=\"fb_login_protocol_scheme\">[^<]*<\/string>/<string name=\"fb_login_protocol_scheme\">fb$FACEBOOK_APP_ID<\/string>/g" "$strings_file"
    facebook_updated=true
  fi
  
  # Update Facebook Client Token
  if [ -n "$FACEBOOK_CLIENT_TOKEN" ]; then
    sed -i.bak "s/<string name=\"facebook_client_token\">[^<]*<\/string>/<string name=\"facebook_client_token\">$FACEBOOK_CLIENT_TOKEN<\/string>/g" "$strings_file"
    facebook_updated=true
  fi
  
  # Update Google Web Client ID
  if [ -n "$GOOGLE_CLIENT_ID_WEB" ]; then
    sed -i.bak "s/<string name=\"google_web_client_id\">[^<]*<\/string>/<string name=\"google_web_client_id\">$GOOGLE_CLIENT_ID_WEB<\/string>/g" "$strings_file"
    google_updated=true
  fi
  
  # Remove backup file
  rm -f "$strings_file.bak"
  
  if [ "$facebook_updated" = true ]; then
    echo -e "${GREEN}Successfully updated Android resources with Facebook values from .env${NC}"
  fi
  if [ "$google_updated" = true ]; then
    echo -e "${GREEN}Successfully updated Android resources with Google values from .env${NC}"
  fi
  if [ "$facebook_updated" = false ] && [ "$google_updated" = false ]; then
    echo -e "${YELLOW}No Android resources updated.${NC}"
  fi
}

# Function to update iOS resources
update_ios_resources() {
  echo -e "${YELLOW}Updating iOS resources...${NC}"
  
  # Load .env file safely
  if ! load_env_file; then
    return 1
  fi
  
  local plist_file="ios/Runner/Info.plist"
  
  if [ ! -f "$plist_file" ]; then
    echo -e "${RED}ERROR: Info.plist file not found at $plist_file${NC}"
    return 1
  fi
  
  local facebook_updated=false
  local google_updated=false
  
  # Update Facebook App ID and related configurations
  if [ -n "$FACEBOOK_APP_ID" ]; then
    echo -e "${YELLOW}Updating Facebook App ID: $FACEBOOK_APP_ID${NC}"
    
    # Update FacebookAppID key - handle multiline format
    sed -i.bak '/FacebookAppID<\/key>/{
      n
      s|<string>[^<]*</string>|<string>'$FACEBOOK_APP_ID'</string>|
    }' "$plist_file"
    
    # Update FacebookAppURLScheme key - handle multiline format
    sed -i.bak '/FacebookAppURLScheme<\/key>/{
      n
      s|<string>[^<]*</string>|<string>fb'$FACEBOOK_APP_ID'</string>|
    }' "$plist_file"
    
    # Update FacebookAppIDPrefix key - handle multiline format
    sed -i.bak '/FacebookAppIDPrefix<\/key>/{
      n
      s|<string>[^<]*</string>|<string>fb'$FACEBOOK_APP_ID'</string>|
    }' "$plist_file"
    
    # Update Facebook URL scheme in CFBundleURLSchemes array (handle both placeholder and existing values)
    sed -i.bak "s|<string>fbYOUR_FACEBOOK_APP_ID</string>|<string>fb$FACEBOOK_APP_ID</string>|g" "$plist_file"
    sed -E -i.bak "s|<string>fb[0-9]+</string>|<string>fb$FACEBOOK_APP_ID</string>|g" "$plist_file"
    
    # Update Facebook URL scheme in LSApplicationQueriesSchemes array (handle both placeholder and existing values)
    sed -i.bak "s|<string>fbYOUR_FACEBOOK_APP_ID</string>|<string>fb$FACEBOOK_APP_ID</string>|g" "$plist_file"
    sed -E -i.bak "s|<string>fb[0-9]+</string>|<string>fb$FACEBOOK_APP_ID</string>|g" "$plist_file"
    
    facebook_updated=true
  fi
  
  # Update Facebook Client Token
  if [ -n "$FACEBOOK_CLIENT_TOKEN" ]; then
    echo -e "${YELLOW}Updating Facebook Client Token${NC}"
    # Update FacebookClientToken key - handle multiline format
    sed -i.bak '/FacebookClientToken<\/key>/{
      n
      s|<string>[^<]*</string>|<string>'$FACEBOOK_CLIENT_TOKEN'</string>|
    }' "$plist_file"
    facebook_updated=true
  fi
  
  # Update FacebookDisplayName (using default app name if FACEBOOK_APP_NAME is not set)
  local facebook_display_name="${FACEBOOK_APP_NAME:-KOC App}"
  if [ -n "$facebook_display_name" ]; then
    echo -e "${YELLOW}Updating Facebook Display Name: $facebook_display_name${NC}"
    # Update FacebookDisplayName key - handle multiline format
    # Escape special characters in the facebook_display_name variable
    local escaped_name=$(printf '%s\n' "$facebook_display_name" | sed 's/[[\.*^$()+?{|]/\\&/g')
    sed -i.bak '/FacebookDisplayName<\/key>/{
      n
      s|<string>[^<]*</string>|<string>'"$escaped_name"'</string>|
    }' "$plist_file"
    facebook_updated=true
  fi
  
  # Update Google Reversed Client ID
  if [ -n "$GOOGLE_REVERSED_CLIENT_ID_IOS" ]; then
    echo -e "${YELLOW}Updating Google Reversed Client ID: $GOOGLE_REVERSED_CLIENT_ID_IOS${NC}"
    # Update in CFBundleURLSchemes array for Google Sign-in
    sed -i.bak "s|<string>com\.googleusercontent\.apps\.[^<]*</string>|<string>$GOOGLE_REVERSED_CLIENT_ID_IOS</string>|g" "$plist_file"
    sed -i.bak "s|<string>REPLACE_WITH_GOOGLE_REVERSED_CLIENT_ID</string>|<string>$GOOGLE_REVERSED_CLIENT_ID_IOS</string>|g" "$plist_file"
    google_updated=true
  fi
  
  # Remove backup file
  rm -f "$plist_file.bak"
  
  if [ "$facebook_updated" = true ]; then
    echo -e "${GREEN}Successfully updated iOS resources with Facebook values from .env${NC}"
    
    # Validate Facebook configuration with more comprehensive checks
    facebook_app_id_found=false
    facebook_client_token_found=false
    facebook_url_schemes_found=false
    
    # Check FacebookAppID key
    if grep -A1 "<key>FacebookAppID</key>" "$plist_file" | grep -q "<string>$FACEBOOK_APP_ID</string>"; then
      facebook_app_id_found=true
      echo -e "${GREEN}✓ Facebook App ID updated successfully${NC}"
    else
      echo -e "${RED}✗ Facebook App ID update may have failed${NC}"
    fi
    
    # Check FacebookClientToken key
    if [ -n "$FACEBOOK_CLIENT_TOKEN" ]; then
      if grep -A1 "<key>FacebookClientToken</key>" "$plist_file" | grep -q "<string>$FACEBOOK_CLIENT_TOKEN</string>"; then
        facebook_client_token_found=true
        echo -e "${GREEN}✓ Facebook Client Token updated successfully${NC}"
      else
        echo -e "${RED}✗ Facebook Client Token update may have failed${NC}"
      fi
    fi
    
    # Check Facebook URL schemes (look for fb + app id pattern in multiple places)
    if grep -q "<string>fb$FACEBOOK_APP_ID</string>" "$plist_file"; then
      facebook_url_schemes_found=true
      echo -e "${GREEN}✓ Facebook URL schemes updated successfully${NC}"
    else
      echo -e "${RED}✗ Facebook URL schemes update may have failed${NC}"
    fi
  fi
  
  if [ "$google_updated" = true ]; then
    echo -e "${GREEN}Successfully updated iOS resources with Google values from .env${NC}"
    
    # Validate Google configuration
    if [ -n "$GOOGLE_REVERSED_CLIENT_ID_IOS" ] && grep -q "<string>$GOOGLE_REVERSED_CLIENT_ID_IOS</string>" "$plist_file"; then
      echo -e "${GREEN}✓ Google Reversed Client ID updated successfully${NC}"
    elif [ -n "$GOOGLE_REVERSED_CLIENT_ID_IOS" ]; then
      echo -e "${RED}✗ Google Reversed Client ID update may have failed${NC}"
    fi
  fi
  
  if [ "$facebook_updated" = false ] && [ "$google_updated" = false ]; then
    echo -e "${YELLOW}No iOS resources updated - missing environment variables.${NC}"
  fi
}

# Our bash functions will update the resource files directly
echo -e "${YELLOW}Checking for Google service configuration files...${NC}"

# Update Android resources with Facebook and Google values from .env
update_android_resources

# Update iOS resources with Facebook and Google values from .env
update_ios_resources

# Create key.properties file for Android signing
echo -e "${YELLOW}Creating Android key.properties file...${NC}"

# Load .env file to get signing configuration safely
load_env_file

# Create key.properties file in android directory
cat > "android/key.properties" << EOF
# Release keystore configuration
# Generated by pre_build.sh from .env file
storePassword=${STORE_PASSWORD}
keyPassword=${KEY_PASSWORD}
keyAlias=${KEY_ALIAS}
storeFile=${STORE_FILE}
EOF
echo -e "${GREEN}Created android/key.properties file.${NC}"

# Check if release keystore exists (check both possible locations)
KEYSTORE_PATH="android/${STORE_FILE:-release.keystore}"
if [ ! -f "$KEYSTORE_PATH" ]; then
  # Also check if it exists in android/app/ directory (downloaded location)
  KEYSTORE_PATH_APP="android/app/${STORE_FILE:-release.keystore}"
  if [ -f "$KEYSTORE_PATH_APP" ]; then
    echo -e "${YELLOW}Found keystore in android/app/ directory. Moving to android/ directory...${NC}"
    mv "$KEYSTORE_PATH_APP" "$KEYSTORE_PATH"
    echo -e "${GREEN}Moved keystore to $KEYSTORE_PATH${NC}"
  else
    echo -e "${RED}WARNING: Release keystore not found at $KEYSTORE_PATH${NC}"
    echo -e "${YELLOW}You need to generate a release keystore or place your existing keystore in the android/ directory${NC}"
    echo -e "${YELLOW}Run the following command to generate a new keystore:${NC}"
    echo -e "${YELLOW}keytool -genkey -v -keystore android/${STORE_FILE:-release.keystore} -keyalg RSA -keysize 2048 -validity 10000 -alias ${KEY_ALIAS:-koc-mobile}${NC}"
  fi
else
  echo -e "${GREEN}Release keystore found at $KEYSTORE_PATH${NC}"
fi

echo ""
echo -e "${GREEN}Pre-build script completed successfully!${NC}"
echo -e "${YELLOW}IMPORTANT: Make sure to replace any placeholder files with actual files from Firebase console for production use.${NC}"
