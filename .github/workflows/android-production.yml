name: Android Production Deploy

on:
  push:
    tags:
      - 'v*'        # Triggers on release tags like v1.0.2
      - '!v*-rc*'   # Excludes RC tags
  workflow_dispatch:  # Keep manual trigger for testing

jobs:
  deploy-production:
    runs-on: ubuntu-latest
    timeout-minutes: 45
    
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Get short commit SHA
        id: get_sha
        run: echo "short_sha=${GITHUB_SHA:0:7}" >> $GITHUB_OUTPUT

      - name: Extract version from tag
        id: get_version
        run: |
          TAG=${GITHUB_REF#refs/tags/}
          echo "Full tag: $TAG"
          
          # Extract version from production tag (e.g., v1.0.3 -> 1.0.3)
          if [[ $TAG =~ ^v([0-9]+\.[0-9]+\.[0-9]+)$ ]]; then
            VERSION=${BASH_REMATCH[1]}
            BUILD_NUMBER=$(date +%s)  # Use timestamp as build number
            FULL_VERSION="${VERSION}+${BUILD_NUMBER}"
            echo "version=$VERSION" >> $GITHUB_OUTPUT
            echo "build_number=$BUILD_NUMBER" >> $GITHUB_OUTPUT
            echo "full_version=$FULL_VERSION" >> $GITHUB_OUTPUT
            echo "is_production=true" >> $GITHUB_OUTPUT
            echo "Extracted version: $VERSION, Build: $BUILD_NUMBER"
          else
            echo "Error: Tag format not recognized. Expected format: v1.0.3"
            exit 1
          fi

      - name: Update pubspec.yaml version
        run: |
          echo "Updating pubspec.yaml version to: ${{ steps.get_version.outputs.full_version }}"
          sed -i "s/^version: .*/version: ${{ steps.get_version.outputs.full_version }}/" pubspec.yaml
          
          # Verify the change
          echo "Updated version in pubspec.yaml:"
          grep "^version:" pubspec.yaml

      - name: Set up Java
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '17'

      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: '3.2'

      - name: Set up Flutter
        uses: subosito/flutter-action@v2
        with:
          channel: stable
      - run: flutter --version

      - name: Install dependencies
        run: flutter pub get

      - name: Install Fastlane
        run: gem install fastlane

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-southeast-1

      - name: Get .env from AWS Secrets Manager
        run: |
          aws secretsmanager get-secret-value \
            --secret-id /prod/koc-app \
            --query SecretString \
            --output text > .env
          if [ ! -s .env ]; then
            echo "Error: .env file is empty or not created"
            exit 1
          fi

      - name: Download files from S3
        run: |
          aws s3 cp s3://prod-jenkins-artifacts-ci/koc-apk/key.properties android/key.properties
          aws s3 cp s3://prod-jenkins-artifacts-ci/koc-apk/release.keystore android/release.keystore
          aws s3 cp s3://prod-jenkins-artifacts-ci/koc-apk/google-services.json android/app/google-services.json

      - name: Run pre_build.sh
        run: |
          chmod +x ./scripts/pre_build.sh
          ./scripts/pre_build.sh

      - name: Verify Fastlane configuration for production
        run: |
          echo "Current Fastlane Appfile package_name:"
          grep "package_name" android/fastlane/Appfile
          
          # Verify it's set to production package name
          if grep -q 'package_name("jp.ne.interspace.koc.app")' android/fastlane/Appfile; then
            echo "✅ Package name is correctly set for production"
          else
            echo "❌ Package name is not set correctly for production"
            echo "Setting correct production package name..."
            sed -i 's/package_name("[^"]*")/package_name("jp.ne.interspace.koc.app")/' android/fastlane/Appfile
          fi

      - name: Create play-store-service-account.json from secret
        run: |
          aws s3 cp s3://koc-app-fastlane-cert/play-store-service-account.json android/fastlane/play-store-service-account.json

      - name: Deploy to Google Play Production Track
        working-directory: android
        run: |
          fastlane production

      # Success notification
      - name: Send Success Notification to Slack
        if: success()
        run: |
          curl -X POST -H 'Content-type: application/json' \            --data '{
              "channel": "#devops-noti",
              "username": "GitHub Actions",
              "icon_url": "https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png",
              "text": "✅ <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|PRODUCTION KOC APP ANDROID - Build SUCCESS>",
              "attachments": [
                {
                  "color": "good",
                  "title": "🚀 Production Build Successful!",
                  "fields": [
                    {
                      "title": "Environment",
                      "value": "production",
                      "short": true
                    },
                    {
                      "title": "Actions Completed",
                      "value": "• GitHub Actions build and test completed\n• Deployed to Google Play Production Track",
                      "short": false
                    },
                    {
                      "title": "App available at",
                      "value": "<https://play.google.com/store/apps/details?id=jp.ne.interspace.koc.app|Google Play Store>",
                      "short": false
                    },
                    {
                      "title": "Build",
                      "value": "#${{ github.run_number }}",
                      "short": true
                    },
                    {
                      "title": "Branch",
                      "value": "${{ github.ref_name }}",
                      "short": true
                    },                    {
                      "title": "Commit",
                      "value": "${{ steps.get_sha.outputs.short_sha }}",
                      "short": true
                    },
                    {
                      "title": "Version",
                      "value": "${{ steps.get_version.outputs.full_version }}",
                      "short": true
                    },
                    {
                      "title": "Duration",
                      "value": "Completed successfully",
                      "short": true
                    }
                  ],
                  "footer": "GitHub Actions CI/CD Pipeline",
                  "footer_icon": "https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png"
                }
              ]
            }' \
            ${{ secrets.SLACK_WEBHOOK_URL }}

      # Failure notification
      - name: Send Failure Notification to Slack
        if: failure()
        run: |
          curl -X POST -H 'Content-type: application/json' \            --data '{
              "channel": "#devops-noti",
              "username": "GitHub Actions",
              "icon_url": "https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png",
              "text": "❌ <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|PRODUCTION KOC APP ANDROID - Build FAILED>",
              "attachments": [
                {
                  "color": "danger",
                  "title": "💥 Production Build Failed!",
                  "fields": [
                    {
                      "title": "Environment",
                      "value": "production",
                      "short": true
                    },
                    {
                      "title": "Failed Step",
                      "value": "Check the <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|workflow logs> for details",
                      "short": false
                    },
                    {
                      "title": "Build",
                      "value": "#${{ github.run_number }}",
                      "short": true
                    },
                    {
                      "title": "Branch",
                      "value": "${{ github.ref_name }}",
                      "short": true
                    },                    {
                      "title": "Commit",
                      "value": "${{ steps.get_sha.outputs.short_sha }}",
                      "short": true
                    },
                    {
                      "title": "Triggered by",
                      "value": "${{ github.actor }}",
                      "short": true
                    },
                    {
                      "title": "Status",
                      "value": "🚨 PRODUCTION BUILD FAILED - URGENT ATTENTION REQUIRED 🚨",
                      "short": false
                    }
                  ],
                  "footer": "GitHub Actions CI/CD Pipeline",
                  "footer_icon": "https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png"
                }
              ]
            }' \
            ${{ secrets.SLACK_WEBHOOK_URL }}

      # Always run notification (for cancelled or other states)
      - name: Send Cancelled/Other Status Notification to Slack
        if: cancelled() || (failure() == false && success() == false)
        run: |
          curl -X POST -H 'Content-type: application/json' \            --data '{
              "channel": "#devops-noti",
              "username": "GitHub Actions",
              "icon_url": "https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png",
              "text": "⚠️ <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|PRODUCTION KOC APP ANDROID - Build CANCELLED/INTERRUPTED>",
              "attachments": [
                {
                  "color": "warning",
                  "title": "⚠️ Production Build Cancelled or Interrupted!",
                  "fields": [
                    {
                      "title": "Environment",
                      "value": "production",
                      "short": true
                    },
                    {
                      "title": "Status",
                      "value": "Build was cancelled or interrupted",
                      "short": false
                    },  
                    {
                      "title": "Build",
                      "value": "#${{ github.run_number }}",
                      "short": true
                    },
                    {
                      "title": "Branch",
                      "value": "${{ github.ref_name }}",
                      "short": true
                    },                    {
                      "title": "Commit",
                      "value": "${{ steps.get_sha.outputs.short_sha }}",
                      "short": true
                    },
                    {
                      "title": "Triggered by",
                      "value": "${{ github.actor }}",
                      "short": true
                    }
                  ],
                  "footer": "GitHub Actions CI/CD Pipeline",
                  "footer_icon": "https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png"
                }
              ]
            }' \
            ${{ secrets.SLACK_WEBHOOK_URL }}
