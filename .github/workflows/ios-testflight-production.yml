name: iOS TestFlight Production Deploy

on:
  push:
    tags:
      - 'v*'        # Triggers on release tags like v1.0.2
      - '!v*-rc*'   # Excludes RC tags
  workflow_dispatch:  # Keep manual trigger for testing

jobs:
  deploy-testflight-production:
    runs-on: macos-latest
    timeout-minutes: 45
    
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Get short commit SHA
        id: get_sha
        run: echo "short_sha=${GITHUB_SHA:0:7}" >> $GITHUB_OUTPUT

      - name: Extract version from tag
        id: get_version
        run: |
          TAG=${GITHUB_REF#refs/tags/}
          echo "Full tag: $TAG"
          
          # Extract version from production tag (e.g., v1.0.3 -> 1.0.3)
          if [[ $TAG =~ ^v([0-9]+\.[0-9]+\.[0-9]+)$ ]]; then
            VERSION=${BASH_REMATCH[1]}
            BUILD_NUMBER=$(date +%s)  # Use timestamp as build number
            FULL_VERSION="${VERSION}+${BUILD_NUMBER}"
            echo "version=$VERSION" >> $GITHUB_OUTPUT
            echo "build_number=$BUILD_NUMBER" >> $GITHUB_OUTPUT
            echo "full_version=$FULL_VERSION" >> $GITHUB_OUTPUT
            echo "is_production=true" >> $GITHUB_OUTPUT
            echo "Extracted version: $VERSION, Build: $BUILD_NUMBER"
          else
            echo "Error: Tag format not recognized. Expected format: v1.0.3"
            exit 1
          fi

      - name: Update pubspec.yaml version
        run: |
          echo "Updating pubspec.yaml version to: ${{ steps.get_version.outputs.full_version }}"
          sed -i '' "s/^version: .*/version: ${{ steps.get_version.outputs.full_version }}/" pubspec.yaml
          
          # Verify the change
          echo "Updated version in pubspec.yaml:"
          grep "^version:" pubspec.yaml

      - name: Set up Xcode 16
        uses: maxim-lobanov/setup-xcode@v1
        with:
          xcode-version: 16.2

      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: '3.2'

      - name: Set up Flutter
        uses: subosito/flutter-action@v2
        with:
          channel: stable
      - run: flutter --version

      - name: Install dependencies
        run: flutter pub get

      - name: Install CocoaPods
        run: |
          cd ios
          pod install --repo-update

      - name: Install Fastlane
        run: gem install fastlane

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-southeast-1

      - name: Get .env from AWS Secrets Manager
        run: |
          aws secretsmanager get-secret-value \
            --secret-id /prod/koc-app \
            --query SecretString \
            --output text > .env
          if [ ! -s .env ]; then
            echo "Error: .env file is empty or not created"
            exit 1
          fi

      - name: Download files from S3
        run: |
          aws s3 cp s3://prod-jenkins-artifacts-ci/koc-apk/GoogleService-Info.plist ios/Runner/GoogleService-Info.plist

      - name: Run pre_build.sh
        run: |
          chmod +x ./scripts/pre_build.sh
          ./scripts/pre_build.sh

      - name: Verify Fastlane configuration for production
        run: |
          echo "Current iOS Fastlane Appfile app_identifier:"
          grep "app_identifier" ios/fastlane/Appfile
          
          # Verify it's set to production app identifier
          if grep -q 'app_identifier("jp.ne.interspace.koc.app")' ios/fastlane/Appfile; then
            echo "✅ App identifier is correctly set for production"
          else
            echo "❌ App identifier is not set correctly for production"
            echo "Setting correct production app identifier..."
            sed -i '' 's|app_identifier(".*")|app_identifier("jp.ne.interspace.koc.app")|' ios/fastlane/Appfile
          fi

      - name: Restore App Store Connect API Key
        run: |
          echo "${{ secrets.APP_STORE_CONNECT_API_KEY_JSON_BASE64 }}" | base64 --decode > ios/fastlane/api_key.json

      - name: Fastlane beta (TestFlight upload)
        working-directory: ios
        env:
          APP_STORE_CONNECT_API_KEY_ID: ${{ secrets.APP_STORE_CONNECT_API_KEY_ID }}
          APP_STORE_CONNECT_ISSUER_ID: ${{ secrets.APP_STORE_CONNECT_ISSUER_ID }}
          APP_STORE_CONNECT_API_KEY: ${{ secrets.APP_STORE_CONNECT_API_KEY }}
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_REGION: ap-southeast-1
          MATCH_PASSWORD: ${{ secrets.MATCH_PASSWORD }}
        run: fastlane beta

      # Success notification
      - name: Send Success Notification to Slack
        if: success()
        run: |
          curl -X POST -H 'Content-type: application/json' \            --data '{
              "channel": "#devops-noti",
              "username": "GitHub Actions",
              "icon_url": "https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png",
              "text": "✅ <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|PRODUCTION KOC APP iOS - Build SUCCESS>",
              "attachments": [
                {
                  "color": "good",
                  "title": "🚀 iOS Production Build Successful!",
                  "fields": [
                    {
                      "title": "Environment",
                      "value": "production",
                      "short": true
                    },
                    {
                      "title": "Platform",
                      "value": "iOS",
                      "short": true
                    },
                    {
                      "title": "Actions Completed",
                      "value": "• GitHub Actions build and test completed\n• Uploaded to TestFlight for production",
                      "short": false
                    },
                    {
                      "title": "App available at",
                      "value": "<https://appstoreconnect.apple.com/apps|App Store Connect - TestFlight>\n<https://apps.apple.com/app/id[APP_ID]|App Store (when approved)>",
                      "short": false
                    },
                    {
                      "title": "Build",
                      "value": "#${{ github.run_number }}",
                      "short": true
                    },
                    {
                      "title": "Branch",
                      "value": "${{ github.ref_name }}",
                      "short": true
                    },                    {
                      "title": "Commit",
                      "value": "${{ steps.get_sha.outputs.short_sha }}",
                      "short": true
                    },
                    {
                      "title": "Version",
                      "value": "${{ steps.get_version.outputs.full_version }}",
                      "short": true
                    },
                    {
                      "title": "Duration",
                      "value": "Completed successfully",
                      "short": true
                    }
                  ],
                  "footer": "GitHub Actions CI/CD Pipeline",
                  "footer_icon": "https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png"
                }
              ]
            }' \
            ${{ secrets.SLACK_WEBHOOK_URL }}

      # Failure notification
      - name: Send Failure Notification to Slack
        if: failure()
        run: |
          curl -X POST -H 'Content-type: application/json' \            --data '{
              "channel": "#devops-noti",
              "username": "GitHub Actions",
              "icon_url": "https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png",
              "text": "❌ <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|PRODUCTION KOC APP iOS - Build FAILED>",
              "attachments": [
                {
                  "color": "danger",
                  "title": "💥 iOS Production Build Failed!",
                  "fields": [
                    {
                      "title": "Environment",
                      "value": "production",
                      "short": true
                    },
                    {
                      "title": "Platform",
                      "value": "iOS",
                      "short": true
                    },
                    {
                      "title": "Failed Step",
                      "value": "Check the <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|workflow logs> for details",
                      "short": false
                    },
                    {
                      "title": "Build",
                      "value": "#${{ github.run_number }}",
                      "short": true
                    },
                    {
                      "title": "Branch",
                      "value": "${{ github.ref_name }}",
                      "short": true
                    },                    {
                      "title": "Commit",
                      "value": "${{ steps.get_sha.outputs.short_sha }}",
                      "short": true
                    },
                    {
                      "title": "Triggered by",
                      "value": "${{ github.actor }}",
                      "short": true
                    },
                    {
                      "title": "Status",
                      "value": "🚨 PRODUCTION iOS BUILD FAILED - URGENT ATTENTION REQUIRED 🚨",
                      "short": false
                    }
                  ],
                  "footer": "GitHub Actions CI/CD Pipeline",
                  "footer_icon": "https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png"
                }
              ]
            }' \
            ${{ secrets.SLACK_WEBHOOK_URL }}

      # Always run notification (for cancelled or other states)
      - name: Send Cancelled/Other Status Notification to Slack
        if: cancelled() || (failure() == false && success() == false)
        run: |
          curl -X POST -H 'Content-type: application/json' \            --data '{
              "channel": "#devops-noti",
              "username": "GitHub Actions",
              "icon_url": "https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png",
              "text": "⚠️ <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|PRODUCTION KOC APP iOS - Build CANCELLED/INTERRUPTED>",
              "attachments": [
                {
                  "color": "warning",
                  "title": "⚠️ iOS Production Build Cancelled or Interrupted!",
                  "fields": [
                    {
                      "title": "Environment",
                      "value": "production",
                      "short": true
                    },
                    {
                      "title": "Platform",
                      "value": "iOS",
                      "short": true
                    },
                    {
                      "title": "Status",
                      "value": "Build was cancelled or interrupted",
                      "short": false
                    },
                    {
                      "title": "Build",
                      "value": "#${{ github.run_number }}",
                      "short": true
                    },
                    {
                      "title": "Branch",
                      "value": "${{ github.ref_name }}",
                      "short": true
                    },                    {
                      "title": "Commit",
                      "value": "${{ steps.get_sha.outputs.short_sha }}",
                      "short": true
                    },
                    {
                      "title": "Triggered by",
                      "value": "${{ github.actor }}",
                      "short": true
                    }
                  ],
                  "footer": "GitHub Actions CI/CD Pipeline",
                  "footer_icon": "https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png"
                }
              ]
            }' \
            ${{ secrets.SLACK_WEBHOOK_URL }}