# CI/CD Workflow Documentation

## Overview

This project uses a tag-based CI/CD workflow with automatic staging and production deployments through GitHub Actions.

## Workflow Process

```
<PERSON><PERSON><PERSON> commits → <PERSON> creates RC tag → GitHub Actions deploy to staging → QC Testing → <PERSON> creates release tag → GitHub Actions deploy to production
```

## Tag Conventions

### RC (Release Candidate) Tags
- **Format**: `v{major}.{minor}.{patch}-rc{number}`
- **Example**: `v1.0.2-rc1`, `v1.2.0-rc2`
- **Purpose**: Triggers staging deployment for QC testing

### Release Tags
- **Format**: `v{major}.{minor}.{patch}`
- **Example**: `v1.0.2`, `v1.2.0`
- **Purpose**: Triggers production deployment

## GitHub Actions Workflows

### Staging Workflows (Triggered by RC tags)
1. **Android Internal Staging** (`android-internal-staging.yml`)
   - Deploys to Google Play Internal Test Track
   - Package: `jp.ne.interspace.koc.app.staging`

2. **iOS TestFlight Staging** (`ios-testflight-staging.yml`)
   - Deploys to TestFlight for staging testing
   - Bundle ID: `jp.ne.interspace.koc.app.staging`

### Production Workflows (Triggered by release tags)
1. **Android Production** (`android-production.yml`)
   - Deploys to Google Play Production Track
   - Package: `jp.ne.interspace.koc.app`

2. **iOS TestFlight Production** (`ios-testflight-production.yml`)
   - Deploys to TestFlight for production release
   - Bundle ID: `jp.ne.interspace.koc.app`

## Deployment Process

### 1. Staging Deployment
1. Jenkins detects merge to develop branch
2. Jenkins creates RC tag (e.g., `v1.0.2-rc1`)
3. GitHub Actions automatically triggered by RC tag
4. App deployed to staging environment
5. QC team tests the staging build

### 2. Production Deployment
1. After QC approval, Jenkins creates release tag (e.g., `v1.0.2`)
2. GitHub Actions automatically triggered by release tag
3. App deployed to production environment
4. Both Android and iOS apps released simultaneously

## Manual Tag Creation

For testing or manual deployments, use the helper script:

```bash
# Make script executable
chmod +x ./scripts/create_tag.sh

# Run the script
./scripts/create_tag.sh
```

Or create tags manually:

```bash
# Create RC tag for staging
git tag -a v1.0.2-rc1 -m "Release Candidate v1.0.2-rc1"
git push origin v1.0.2-rc1

# Create release tag for production
git tag -a v1.0.2 -m "Production Release v1.0.2"
git push origin v1.0.2
```

## Environment Configuration

### Staging Environment
- **AWS Secret**: `/dev/koc-app`
- **S3 Bucket**: `dev-jenkins-artifacts-ci`
- **Android Package**: `jp.ne.interspace.koc.app.staging`
- **iOS Bundle**: `jp.ne.interspace.koc.app.staging`

### Production Environment
- **AWS Secret**: `/prod/koc-app`
- **S3 Bucket**: `prod-jenkins-artifacts-ci`
- **Android Package**: `jp.ne.interspace.koc.app`
- **iOS Bundle**: `jp.ne.interspace.koc.app`

## Required GitHub Secrets

- `AWS_ACCESS_KEY_ID`
- `AWS_SECRET_ACCESS_KEY`
- `APP_STORE_CONNECT_API_KEY_ID`
- `APP_STORE_CONNECT_ISSUER_ID`
- `APP_STORE_CONNECT_API_KEY`
- `MATCH_PASSWORD`

## QC Process

1. **RC Deployment**: After RC tag is created, staging deployment begins
2. **QC Testing**: QC team tests the staging build
3. **QC Approval**: If tests pass, QC team approves for production
4. **Production Release**: Jenkins creates release tag triggering production deployment

## Troubleshooting

### Workflow Not Triggered
- Ensure tag follows correct format
- Check if tag was pushed to repository
- Verify GitHub Actions are enabled

### Deployment Failures
- Check AWS credentials and permissions
- Verify secrets are properly configured
- Review build logs in GitHub Actions

### Package Name Issues
- Staging uses `.staging` suffix
- Production uses base package name
- Fastlane configuration updated automatically

## Monitoring

- Check GitHub Actions tab for workflow status
- Monitor Google Play Console for Android releases
- Monitor App Store Connect for iOS releases
- Review AWS CloudWatch logs for deployment issues
