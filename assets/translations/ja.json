{"appTitle": "koc_app", "helloWorld": "Hello World!", "signInOrCreateAccount": "Login or create a free account to continue", "continueWithFacebook": "Continue with Facebook", "continueWithGoogle": "Continue with Google", "existsEmailContinueLogin": "This email address is already exist, click Continue to <PERSON>gin", "createAnAccount": "Create an account", "__termsDefinition__": "", "termsDefinitionAdultPornographyAndViolence": "Promoting something through sites with illegal, violent or pornographic content.", "termsDefinitionBrandBidding": "Creating ads by bidding on branded terms and keywords (including any variations or misspellings of the brand) to gain favorable listings on search engine platforms.", "termsDefinitionCashback": "The use of cashback to drive traffic to the advertiser's site.", "termsDefinitionCouponAndDiscountCodes": "The use of coupon, discount code, voucher, or similar type to drive traffic to the advertiser's site.", "termsDefinitionDeepLinking": "To link visitors to a subpage. Usually to a specific product page on the advertiser's site instead of the homepage.", "termsDefinitionDirectLinking": "To send visitors from an ad directly to an advertiser's offer, eliminating the requirement for a landing page.", "termsDefinitionDisplayAds": "The use of paid ads in the form of banners or other visual formats on the websites to drive traffic to the advertiser's site.", "termsDefinitionDisplayBanner": "The use of banner displays on the websites to drive traffic to the advertiser's site without paid ads.", "termsDefinitionEmailMarketing": "The use of email marketing to drive traffic to the advertiser's site.", "termsDefinitionGambling": "Promoting through sites with gambling content.", "termsDefinitionIncentiveTrafficLoyalty": "The use of membership points, miles, or other similar reward compensation to drive traffic to the advertiser's site.", "termsDefinitionInterstitial": "The use of interstitial ads to drive traffic to the advertiser's site.\n\nInterstitial: Full-screen placements between standard interactions in the user experience of a site, app, or game. For example, navigation between two articles on a news media website or the transition between levels of a hyper-casual game can warrant one of these ads.", "termsDefinitionNativeAds": "Creating ads that blend in with the content of the website, appearing as sponsored articles or recommendations to drive traffic to the advertiser's site.", "termsDefinitionPopUpPopUnder": "The use of pop ads to drive traffic to the advertiser's site.\n\nPop-Up: These are windows or tabs that open after a user visits a page. The visitor will see the ad \"pops up\" over the current browsing window (or sometimes on a new tab).\n\nPop-Under: The only difference is that pop-under appears underneath the page that visitors see at the moment and the ad is visible after the user closes this page.", "termsDefinitionPushNotification": "The use of push notifications on desktop or mobile to drive traffic to the advertiser's site.", "termsDefinitionSearchEngineMarketing": "Promoting by buying ad space in search engine results to appear above all other rankings.", "termsDefinitionSelfConversion": "Clicking your own affiliate link and making conversions by yourself.", "termsDefinitionSocialMediaAds": "The use of paid ads on social media sites, online communities, or other social portals such as Facebook, Instagram, Youtube, TikTok etc., to drive traffic to the advertiser's site.", "termsDefinitionSocialMediaPlatform": "The use of social media sites, online communities, or other social portals such as Facebook, Instagram, Youtube, TikTok etc., to drive traffic to the advertiser's site without paid ads.", "termsDefinitionSocialMessagerApp": "The use of social messager channels such as WhatsApp, WeChat, Line, Messager etc., to drive traffic to the advertiser's site.", "termsDefinitionSubAffiliateNetwork": "Promoting through a network of individual publishers.\n* If permitted, publisher must be completely transparent with regards to where traffic from sub-affiliates originated.", "_voucherCode_": "", "voucherCategory01": "Travel & Hotels", "voucherCategory02": "Electronics", "voucherCategory03": "Fashion", "voucherCategory04": "Beauty & Health", "voucherCategory05": "Home & Living", "voucherCategory06": "Food & Grocery"}