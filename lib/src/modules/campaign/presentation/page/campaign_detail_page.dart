import 'package:koc_app/src/shared/constants/color_constants.dart';
import 'package:koc_app/src/shared/widgets/cached_image_with_placeholder.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/modules/campaign/cubit/campaign_detail_cubit.dart';
import 'package:koc_app/src/modules/campaign/cubit/campaign_detail_state.dart';
import 'package:koc_app/src/modules/campaign/cubit/creative_cubit.dart';
import 'package:koc_app/src/modules/campaign/cubit/creative_state.dart';
import 'package:koc_app/src/modules/campaign/custom_link/cubit/custom_link_cubit.dart';
import 'package:koc_app/src/modules/campaign/custom_link/cubit/custom_link_history_cubit.dart';
import 'package:koc_app/src/modules/campaign/custom_link/presentation/custom_link_history_page.dart';
import 'package:koc_app/src/modules/campaign/custom_link/presentation/widget/custom_link_generation_view.dart';
import 'package:koc_app/src/modules/campaign/data/models/campaign.dart';
import 'package:koc_app/src/modules/home/<USER>/data/model/voucher.dart';
import 'package:koc_app/src/modules/home/<USER>/presentation/widget/voucher_card.dart';
import 'package:koc_app/src/shared/data/image_data.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/mixin/common_mixin.dart';
import 'package:koc_app/src/shared/utils/intent_utils.dart';
import 'package:koc_app/src/shared/widgets/common_image_slider.dart';
import 'package:koc_app/src/shared/widgets/common_loading.dart';
import 'package:koc_app/src/shared/widgets/common_tab.dart';
import 'package:material_symbols_icons/material_symbols_icons.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../../../../shared/services/shared_preferences_service.dart';
import '../../custom_link/data/model/custom_link.dart';
import '../widget/reward_value_widget.dart';

class CampaignDetailPage extends StatefulWidget {
  final int campaignId;
  final double modalHeight;
  final List<HighestRewardSummary>? highestRewardSummaries;

  const CampaignDetailPage(this.campaignId, this.modalHeight, {this.highestRewardSummaries, super.key});

  @override
  State<CampaignDetailPage> createState() => _CampaignDetailPageState();
}

class _CampaignDetailPageState extends State<CampaignDetailPage> with CommonMixin {
  int _tabIndex = 0;
  bool _showStickyBottom = false;
  double modalHeight = 1;
  final _sharedPreferencesService = Modular.get<SharedPreferencesService>();
  final _viewKey = GlobalKey<CustomLinkGenerationViewState>();
  late final CampaignDetailCubit _campaignDetailCubit;
  late final CustomLinkCubit _customLinkCubit;
  List<HighestRewardSummary>? _highestRewardSummaries;
  late final ScrollController _scrollController;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _campaignDetailCubit = ReadContext(context).read<CampaignDetailCubit>();
    _customLinkCubit = ReadContext(context).read<CustomLinkCubit>();
    _highestRewardSummaries = widget.highestRewardSummaries;
  }

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initData();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _initData() async {
    try {
      showLoadingDialog(context);
      await _campaignDetailCubit.fetchCampaignDetails(widget.campaignId);
      await _campaignDetailCubit.checkCustomLinksEnabled(widget.campaignId);
      if (mounted) {
        hideLoadingDialog(context);
      }
    } catch (e) {
      if (mounted) {
        hideLoadingDialog(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to load campaign details. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        color: Colors.white,
        child: BlocBuilder<CampaignDetailCubit, CampaignDetailState>(
          builder: (context, state) {
            if (state.campaignDetails != null) {
              CampaignDetails? campaignDetails = state.campaignDetails;
              return Column(
                spacing: 16.r,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildHeader(campaignDetails!, state.isCustomLinksEnabled),
                  _buildBody(context, campaignDetails),
                  _buildStickyBottom(campaignDetails)
                ],
              );
            }
            return const Center(
              child: SizedBox.shrink(),
            );
          },
        ),
      ),
      bottomSheet: _tabIndex == 3 ? _buildGenerateButton() : null,
    );
  }

  Widget _buildGenerateButton() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.all(16.r),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: (_viewKey.currentState?.isValid ?? false)
              ? () async {
                  await _viewKey.currentState?.generate();
                }
              : null,
          child: Text(
            'Generate',
            style: context.textLabelLarge(color: Colors.white),
          ),
        ),
      ),
    );
  }

  Future<AffiliationStatus?> _showConfirmationModal(int campaignId) async {
    return showDialog<AffiliationStatus?>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.white,
          actionsAlignment: MainAxisAlignment.end,
          title: Text(
            'Confirmation',
            style: Theme.of(context).textTheme.bodyMedium!.copyWith(color: const Color(0xFF464646)),
          ),
          content: Text(
            'Do you accept all terms and conditions for the promotion of selected campaigns?',
            style: Theme.of(context).textTheme.labelLarge!.copyWith(color: const Color(0xFF464646)),
          ),
          actions: <Widget>[
            TextButton(
              child: Text(
                'No',
                style: Theme.of(context).textTheme.labelLarge!.copyWith(color: const Color(0xFFEF6507)),
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            ElevatedButton(
              child: Text(
                'Yes, I accept',
                style: Theme.of(context).textTheme.labelLarge!.copyWith(color: Colors.white),
              ),
              onPressed: () async {
                final dialogContext = context;
                final statusResult = await _applyCampaign(campaignId);
                if (dialogContext.mounted) {
                  Navigator.of(dialogContext).pop(statusResult);
                }
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildStickyBottom(CampaignDetails campaignDetails) {
    final currency = campaignDetails.currency ?? '';
    final rewardsToShow =
        (_highestRewardSummaries != null && _highestRewardSummaries!.isNotEmpty) ? _highestRewardSummaries : null;
    if (_isFullScreen() && _showStickyBottom) {
      return Container(
          width: double.infinity,
          padding: EdgeInsets.all(16.r),
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.r),
                topRight: Radius.circular(12.r),
              ),
              border: Border.all(color: Colors.grey[200]!, width: 1.r)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  spacing: 4.r,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      spacing: 4.r,
                      children: [
                        Flexible(
                          child: Text(
                            campaignDetails.name,
                            style: Theme.of(context).textTheme.labelLarge!.copyWith(fontWeight: FontWeight.bold),
                          ),
                        ),
                        Container(
                          alignment: Alignment.center,
                          padding: EdgeInsets.only(left: 4.r, right: 4.r, top: 2.r, bottom: 0.r),
                          decoration: BoxDecoration(borderRadius: BorderRadius.circular(6.r), color: Colors.white),
                          child: rewardsToShow != null
                              ? Text(
                                  RewardValueWidget.formatRewardValue(
                                    reward: rewardsToShow.first.reward,
                                    type: rewardsToShow.first.type,
                                    currency: currency,
                                  ),
                                  style:
                                      Theme.of(context).textTheme.labelMedium!.copyWith(color: const Color(0xFF15793E)),
                                )
                              : RewardValueWidget(
                                  rewards: campaignDetails.defaultRewards,
                                  currency: currency,
                                ),
                        )
                      ],
                    ),
                    Text(
                      campaignDetails.categories.isNotEmpty
                          ? campaignDetails.categories.map((e) => e.name).join(', ')
                          : '-',
                      style: Theme.of(context).textTheme.labelMedium!.copyWith(color: const Color(0xFF767676)),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    )
                  ],
                ),
              ),
              SizedBox(width: 8.r),
              _buildCampaignStatusButton(campaignDetails),
            ],
          ));
    }
    return const SizedBox.shrink();
  }

  Widget _buildBody(BuildContext context, CampaignDetails campaignDetails) {
    return Expanded(
      child: SingleChildScrollView(
        controller: _scrollController,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 16.r,
          children: [
            campaignDetails.affiliationStatus == AffiliationStatus.NEW ||
                    campaignDetails.affiliationStatus == AffiliationStatus.APPROVED
                ? VisibilityDetector(
                    key: const Key('campaign-card'),
                    child: _buildCampaignCard(context, campaignDetails),
                    onVisibilityChanged: (info) {
                      double visiblePercentage = info.visibleFraction * 100;
                      if (mounted &&
                          campaignDetails.campaignStatus == CampaignStatus.RUNNING &&
                          (campaignDetails.affiliationStatus == AffiliationStatus.NEW ||
                              campaignDetails.affiliationStatus == AffiliationStatus.APPROVED)) {
                        setState(() {
                          _showStickyBottom = visiblePercentage == 0;
                        });
                      }
                    },
                  )
                : _buildCampaignCard(context, campaignDetails),
            _buildContents(context, campaignDetails)
          ],
        ),
      ),
    );
  }

  Widget _buildContents(BuildContext context, CampaignDetails campaignDetails) {
    if (campaignDetails.affiliationStatus == AffiliationStatus.APPROVED) {
      return _buildTabs(context, campaignDetails);
    }
    return _buildCampaignDetails(context, campaignDetails);
  }

  Widget _buildTabs(BuildContext context, CampaignDetails campaignDetails) {
    return BlocBuilder<CampaignDetailCubit, CampaignDetailState>(
      builder: (context, state) {
        final tabs = [
          CommonTab(CampaignDetailsTabIndex.DETAILS.label),
          CommonTab(CampaignDetailsTabIndex.BANNERS.label),
          CommonTab(CampaignDetailsTabIndex.COUPONS.label),
          if (state.isCustomLinksEnabled) CommonTab(CampaignDetailsTabIndex.CUSTOM_LINKS.label),
        ];

        return DefaultTabController(
          length: tabs.length,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TabBar(
                onTap: (index) async {
                  setState(() {
                    _tabIndex = index;
                  });

                  CreativeCubit creativeCubit = ReadContext(context).read<CreativeCubit>();
                  if (index == 1 && !creativeCubit.state.isDataLoaded) {
                    await creativeCubit.getCreatives(campaignDetails.id);
                  }

                  if (index == 2) {
                    final siteId = await _sharedPreferencesService.getCurrentSiteId();
                    FindVouchersRequest request = FindVouchersRequest(siteId: siteId!, campaignId: campaignDetails.id);
                    await _campaignDetailCubit.findVouchers(request);
                  }

                  if ((index == 3 || index == tabs.length - 1) && state.isCustomLinksEnabled) {
                    List<AcceptedUrl> acceptedUrls = await _customLinkCubit.getAcceptedUrls(campaignDetails.id);
                    _campaignDetailCubit.updateAcceptedUrls(acceptedUrls);
                  }
                },
                isScrollable: true,
                labelStyle: Theme.of(context).textTheme.labelLarge,
                tabs: tabs,
              ),
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                child: _buildTabContent(campaignDetails),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTabContent(CampaignDetails campaignDetails) {
    switch (_tabIndex) {
      case 0:
        return _buildCampaignDetails(context, campaignDetails);
      case 1:
        return _buildBanners(campaignDetails.id);
      case 2:
        return _buildCoupons(campaignDetails.id);
      case 3:
        return _buildCustomLink(
            campaignDetails.id, _campaignDetailCubit.state.acceptedUrls.map((item) => item.displayValue).toList());
      default:
        return _buildCampaignDetails(context, campaignDetails);
    }
  }

  Widget _buildCustomLink(int campaignId, List<String> customLinkAcceptUrls) {
    return Padding(
      padding: EdgeInsets.only(left: 16.r, right: 16.r, bottom: 80.r),
      child: MultiBlocProvider(
          providers: [
            BlocProvider.value(value: ReadContext(context).read<CustomLinkCubit>()),
            BlocProvider.value(
              value: ReadContext(context).read<CustomLinkHistoryCubit>(),
            ),
          ],
          child: CustomLinkGenerationView(
            key: _viewKey,
            onChange: _onChange,
            customLinkAcceptUrls: customLinkAcceptUrls,
            campaignId: campaignId,
            showPropertyFilter: false,
            showCampaignFilter: false,
          )),
    );
  }

  Widget _buildCoupons(int campaignId) {
    return BlocBuilder<CampaignDetailCubit, CampaignDetailState>(builder: (context, state) {
      if (state.loadingVouchers) {
        return const CommonLoading();
      }

      if (state.vouchers.isEmpty) {
        return _buildNoData(Symbols.confirmation_number_sharp, 'coupons');
      }

      return Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.r),
        child: Column(
          spacing: 12.r,
          children: [
            const SizedBox.shrink(),
            ...state.vouchers.map((voucher) => VoucherCard(voucher)),
          ],
        ),
      );
    });
  }

  void _copyClipboard(String couponCode) {
    Clipboard.setData(ClipboardData(text: couponCode)).then((_) {
      if (mounted) {
        context.showSnackBar('Copied to clipboard!');
      }
    });
  }

  Widget _buildNoData(IconData iconData, String target) {
    return SizedBox(
      width: double.infinity,
      height: 330.r,
      child: Column(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            iconData,
            color: const Color(0xFFFFB522),
            size: 80.r,
            weight: 200,
          ),
          Text(
            'No $target are available at the moment',
            style: context.textLabelLarge(color: ColorConstants.hintColor),
          )
        ],
      ),
    );
  }

  Widget _buildBanners(int campaignId) {
    return BlocBuilder<CreativeCubit, CreativeState>(builder: (context, state) {
      if (state.isLoading) {
        return const CommonLoading();
      }

      if (state.errorMessage.isNotEmpty) {
        return Center(
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 48.r),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, color: Colors.red, size: 48.0),
                SizedBox(height: 12.r),
                const Text(
                  'Error loading banners',
                  style: TextStyle(fontSize: 16),
                ),
              ],
            ),
          ),
        );
      }

      if (state.isDataLoaded && state.creatives.isEmpty) {
        return _buildNoData(Symbols.photo_library_sharp, 'banners');
      }

      if (state.isDataLoaded) {
        return Column(
          spacing: 12.r,
          children: [
            const SizedBox.shrink(),
            ...state.creatives.asMap().entries.map((entry) {
              final creativeIndex = entry.key;
              final creative = entry.value;

              final currentImageIndex =
                  state.imageIndex.containsKey(creativeIndex) ? state.imageIndex[creativeIndex]! : 0;

              return Column(
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16.r),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Flexible(
                          child: Text(
                            creative.name ?? '',
                            style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.w500),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                        _buildPopupMenuButton(
                            creative.creatives.isNotEmpty
                                ? creative.creatives[currentImageIndex].affiliateLink ?? ''
                                : '',
                            creative.creatives.isNotEmpty ? creative.creatives[currentImageIndex].imageUrl : null,
                            null,
                            creativeIndex)
                      ],
                    ),
                  ),
                  CommonImageSlider(
                    creative.creatives
                        .where((item) => item.imageUrl != null && item.imageUrl!.isNotEmpty)
                        .map((item) => ImageData(imageUrl: item.imageUrl!, width: item.width, height: item.height))
                        .toList(),
                    showPage: true,
                    showSize: true,
                    onPageChanged: (index) {
                      ReadContext(context).read<CreativeCubit>().updateImageIndex(creativeIndex, index);
                    },
                  ),
                ],
              );
            })
          ],
        );
      }

      return const CommonLoading();
    });
  }

  Widget _buildPopupMenuButton(String shareLink, String? downloadUrl, String? couponCode, [int? creativeIndex]) {
    return PopupMenuButton<int>(
      color: Colors.white,
      iconSize: 20.r,
      icon: const Icon(Icons.more_vert, color: Colors.black),
      onSelected: (int value) {
        if (value == 0) {
          Share.share(shareLink);
        } else if (value == 1) {
          if (downloadUrl != null) {
            _downloadImage(downloadUrl);
          }
        } else if (value == 2) {
          if (couponCode != null) {
            _copyClipboard(couponCode);
          }
        }
      },
      itemBuilder: (BuildContext context) {
        return [
          _buildPopupMenuItem(0, Icons.share_outlined, 'Share link'),
          if (downloadUrl != null) _buildPopupMenuItem(1, Icons.download, 'Download Image'),
          if (couponCode != null) _buildPopupMenuItem(2, Icons.copy, 'Copy coupon code'),
        ];
      },
    );
  }

  PopupMenuItem<int> _buildPopupMenuItem(int value, IconData iconData, String text) {
    return PopupMenuItem<int>(
      value: value,
      child: Row(
        spacing: 16.r,
        children: [
          Icon(
            iconData,
            size: 20.r,
          ),
          Text(
            text,
            style: Theme.of(context).textTheme.labelLarge,
          ),
        ],
      ),
    );
  }

  Future<String> _downloadImage(String imageUrl) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final fileName = imageUrl.split('/').last;
      final filePath = '${directory.path}/$fileName';

      final dio = Dio();
      await dio.download(imageUrl, filePath);

      if (mounted) {
        _saveImageToGallery(filePath);
      }

      return filePath;
    } catch (e) {
      debugPrint('Error in downloadImage: ${e.toString()}');
      if (mounted) {
        _showErrorSnackBar('Failed to download image. Please try again.');
      }
      return '';
    }
  }

  void _saveImageToGallery(String filePath) async {
    try {
      await saveImageToDownloadFolder(context, filePath);
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Failed to save image to gallery.');
      }
    }
  }

  void _showErrorSnackBar(String message) {
    context.showSnackBar(message, durationSecond: 3);
  }

  Widget _buildCampaignDetails(BuildContext context, CampaignDetails campaignDetails) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.r),
      child: Column(
        spacing: 16.r,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox.shrink(),
          _buildDescription(context, campaignDetails.description),
          _buildTargetDevices(
            context,
            campaignDetails.deviceTypes ?? [],
          ),
          _buildNavigators(
            context,
            campaignDetails,
          ),
        ],
      ),
    );
  }

  Widget _buildNavigators(
    BuildContext context,
    CampaignDetails campaignDetails,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildNavigator('Reward', '/campaign/reward', campaignDetails, context),
        _buildNavigator('Conversion', '/campaign/conversion', campaignDetails, context),
        _buildNavigator('Traffic restrictions', '/campaign/traffic-restrictions', campaignDetails, context),
      ],
    );
  }

  Widget _buildNavigator(String title, String route, Object? arguments, BuildContext context) {
    return GestureDetector(
      onTap: () {
        Modular.to.pushNamed(route, arguments: arguments);
      },
      child: Padding(
        padding: EdgeInsets.only(bottom: 16.r),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.w500),
            ),
            Icon(
              Icons.arrow_forward,
              size: 20.r,
            )
          ],
        ),
      ),
    );
  }

  Widget _buildTargetDevices(BuildContext context, List<String> targetDevices) {
    final deviceCategories = {
      'PC Browser': [TargetDeviceType.DESKTOP],
      'Mobile Browser': [TargetDeviceType.IPHONE, TargetDeviceType.ANDROID],
      'Tablet Browser': [TargetDeviceType.ANDROID_TAB, TargetDeviceType.IPAD],
      'Mobile App': [TargetDeviceType.IPHONE, TargetDeviceType.ANDROID],
      'Tablet App': [TargetDeviceType.ANDROID_TAB, TargetDeviceType.IPAD],
    };

    return Column(
      spacing: 8.r,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Target devices',
          style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.w500),
        ),
        ...deviceCategories.entries.map((entry) {
          final supportedDevices = _getSupportedDevices(targetDevices, entry.value);
          return _buildTargetDevicesItem(entry.key, supportedDevices, context);
        }),
      ],
    );
  }

  List<String> _getSupportedDevices(List<String> targetDevices, List<TargetDeviceType> deviceTypes) {
    final result = <String>[];

    for (final deviceType in deviceTypes) {
      if (targetDevices.contains(deviceType.name) || targetDevices.contains(deviceType.value)) {
        result.add(deviceType.value);
      }
    }

    return result;
  }

  Widget _buildTargetDevicesItem(String name, List<String> values, BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(name, style: Theme.of(context).textTheme.labelLarge),
        values.isNotEmpty
            ? Text(values.join(', '), style: Theme.of(context).textTheme.labelLarge)
            : Text('No', style: Theme.of(context).textTheme.labelLarge!.copyWith(color: const Color(0xFFA72D1A))),
      ],
    );
  }

  Widget _buildDescription(
    BuildContext context,
    String description,
  ) {
    return Column(
      spacing: 8.r,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Description',
          style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.w500),
        ),
        Text(
          description,
          style: Theme.of(context).textTheme.labelLarge,
        ),
      ],
    );
  }

  bool _isHalfScreen() {
    return widget.modalHeight == 0.5;
  }

  bool _isFullScreen() {
    return widget.modalHeight == 1.0;
  }

  Widget _buildHeader(CampaignDetails campaignDetails, bool isCustomLinkEnabled) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.r),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              campaignDetails.name,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(fontSize: 18, fontWeight: FontWeight.w500),
              overflow: _isHalfScreen() ? null : TextOverflow.ellipsis,
            ),
          ),
          Row(
            spacing: 12.r,
            children: [
              if (isCustomLinkEnabled &&
                  campaignDetails.affiliationStatus == AffiliationStatus.APPROVED &&
                  campaignDetails.campaignStatus == CampaignStatus.RUNNING)
                GestureDetector(
                  onTap: () {
                    _openCustomLinksHistoryModal(campaignDetails.id);
                  },
                  child: CircleAvatar(
                    backgroundColor: Colors.grey[200],
                    radius: 12.r,
                    child: Icon(Icons.history, size: 16.r),
                  ),
                ),
              GestureDetector(
                onTap: () {
                  Navigator.pop(context);
                },
                child: CircleAvatar(
                  backgroundColor: Colors.grey[200],
                  radius: 12.r,
                  child: Icon(Icons.close, size: 16.r),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCampaignCard(BuildContext context, CampaignDetails campaignDetails) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.r),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12.r),
        child: Container(
          color: Colors.grey[200],
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: EdgeInsets.all(8.r),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12.r),
                  border: Border.all(
                    color: Colors.grey[300]!,
                    width: 1.r,
                  ),
                  color: Colors.white,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SizedBox(
                      height: 80.r,
                      child: AspectRatio(
                        aspectRatio: 1.0,
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(12.r),
                          child: CachedImageWithPlaceholder(
                            imageUrl: campaignDetails.imageUrl ?? '',
                            fit: BoxFit.contain,
                          ),
                        ),
                      ),
                    ),
                    _buildCampaignStatusButton(campaignDetails),
                  ],
                ),
              ),
              Padding(
                padding: EdgeInsets.all(12.0.r),
                child: Column(
                  spacing: 8.r,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildCampaignCardDescriptionItem(context, 'Type', campaignDetails.type.name),
                    _buildCampaignCardDescriptionItem(
                        context,
                        'Category',
                        campaignDetails.categories.isNotEmpty
                            ? campaignDetails.categories.map((e) => e.name).join(', ')
                            : '-'),
                    _buildCampaignCardDescriptionItem(
                        context, 'Period', _formatPeriod(campaignDetails.startDate, campaignDetails.endDate)),
                    _buildCampaignCardDescriptionItem(context, 'Website', campaignDetails.url, isLink: true),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCampaignStatusButton(CampaignDetails campaignDetails) {
    if (campaignDetails.affiliationStatus == AffiliationStatus.NEW) {
      return ElevatedButton(
        onPressed: () async {
          final localContext = context;
          AffiliationStatus? result = await _showConfirmationModal(campaignDetails.id);
          if (result != null && localContext.mounted) {
            Navigator.of(localContext).pop(result);
          }
        },
        child: Text(
          'Apply now',
          style: Theme.of(context).textTheme.labelLarge!.copyWith(color: Colors.white),
        ),
      );
    } else if (campaignDetails.affiliationStatus == AffiliationStatus.APPLYING) {
      return Container(
        padding: EdgeInsets.symmetric(horizontal: 8.r, vertical: 2.r),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6.r),
          border: Border.all(color: Colors.grey[300]!, width: 1.r),
        ),
        child: Column(
          children: [
            Text('Applied', style: Theme.of(context).textTheme.labelMedium!.copyWith(color: const Color(0xFF767676))),
            Text(campaignDetails.appliedDate?.toFormattedString() ?? '',
                style: Theme.of(context).textTheme.labelMedium!.copyWith(color: const Color(0xFF767676))),
          ],
        ),
      );
    } else if (campaignDetails.affiliationStatus == AffiliationStatus.APPROVED &&
        campaignDetails.campaignStatus != CampaignStatus.PAUSED) {
      return ElevatedButton(
        onPressed: () {
          _shareCampaignLink(campaignDetails);
        },
        child: Text(
          'Promote now',
          style: Theme.of(context).textTheme.labelLarge!.copyWith(color: Colors.white),
        ),
      );
    } else if (campaignDetails.affiliationStatus == AffiliationStatus.APPROVED &&
        campaignDetails.campaignStatus == CampaignStatus.PAUSED) {
      return Container(
        padding: EdgeInsets.symmetric(horizontal: 8.r, vertical: 2.r),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6.r),
          border: Border.all(color: Colors.grey[300]!, width: 1.r),
        ),
        child: Column(
          children: [
            Text('Paused', style: Theme.of(context).textTheme.labelMedium!.copyWith(color: const Color(0xFF767676))),
            Text(campaignDetails.endDate?.toFormattedString() ?? '',
                style: Theme.of(context).textTheme.labelMedium!.copyWith(color: const Color(0xFF767676))),
          ],
        ),
      );
    }
    return Container();
  }

  Future<AffiliationStatus> _applyCampaign(int campaignId) async {
    final currentContext = context;
    showLoadingDialog(currentContext);
    CampaignDetailCubit cubit = _campaignDetailCubit;
    try {
      final status = await cubit.applyCampaign(campaignId);
      return status == AffiliationStatus.NEW ? AffiliationStatus.APPLYING : status;
    } catch (e) {
      return AffiliationStatus.APPLYING;
    } finally {
      if (mounted && currentContext.mounted) {
        Navigator.of(currentContext).pop();
      }
    }
  }

  Widget _buildCampaignCardDescriptionItem(BuildContext context, String title, String value, {bool isLink = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.labelMedium,
        ),
        SizedBox(width: 8.r),
        Expanded(
          child: isLink
              ? GestureDetector(
                  onTap: () {
                    IntentUtils.openBrowserURL(url: value);
                  },
                  child: Text(
                    value,
                    style: Theme.of(context).textTheme.labelMedium!.copyWith(color: const Color(0xFFEF6507)),
                    textAlign: TextAlign.right,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                )
              : Text(
                  value,
                  style: Theme.of(context).textTheme.labelMedium,
                  textAlign: TextAlign.right,
                  softWrap: true,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 3,
                ),
        ),
      ],
    );
  }

  String _formatPeriod(DateTime? startDate, DateTime? endDate) {
    if (startDate == null) {
      return '-';
    }

    if (endDate == null) {
      return startDate.toFormattedString();
    }

    return '${startDate.toFormattedString()} - ${endDate.toFormattedString()}';
  }

  void _shareCampaignLink(CampaignDetails campaignDetails) {
    CampaignDetailCubit cubit = _campaignDetailCubit;
    if (cubit.state.quickLink != '') {
      Share.share(cubit.state.quickLink);
    } else {
      context.showSnackBar('Affiliated link not found. Please try again!');
    }
  }

  Future<void> _openCustomLinksHistoryModal(int campaignId) async {
    CustomLinkHistoryCubit historyCubit = ReadContext(context).read<CustomLinkHistoryCubit>();
    showModalBottomSheet(
      useSafeArea: true,
      isScrollControlled: true,
      context: context,
      builder: (BuildContext context) {
        return BlocProvider.value(
          value: historyCubit,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12.r),
            child: CustomLinkHistoryPage(
              campaignId: campaignId,
              showPropertyFilter: false,
              showCampaignFilter: false,
            ),
          ),
        );
      },
    );
  }

  void _onChange() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {});
    });
  }
}
