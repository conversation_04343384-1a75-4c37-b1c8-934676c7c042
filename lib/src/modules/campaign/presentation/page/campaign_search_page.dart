import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/campaign/cubit/campaign_search_cubit.dart';
import 'package:koc_app/src/modules/campaign/cubit/campaign_search_state.dart';
import 'package:koc_app/src/modules/campaign/data/models/campaign.dart';
import 'package:koc_app/src/modules/campaign/presentation/mixin/campaign_detail_mixin.dart';
import 'package:koc_app/src/modules/home/<USER>/presentation/widget/empty_state_widget.dart';
import 'package:koc_app/src/modules/report/mixin/report_mixin.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/widgets/cached_image_with_placeholder.dart';

class CampaignSearchPage extends StatefulWidget {
  const CampaignSearchPage({super.key});

  @override
  State<CampaignSearchPage> createState() => _CampaignSearchPageState();
}

class _CampaignSearchPageState extends BasePageState<CampaignSearchPage, CampaignSearchCubit>
    with CampaignDetailMixin, ReportMixin {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  final ScrollController _scrollController = ScrollController();
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    initialize();
    _scrollController.addListener(_onScroll);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _controller.dispose();
    _focusNode.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent * 0.8) {
      if (cubit.state.hasMoreData && !cubit.state.isLoadingMore) {
        cubit.loadMoreCampaigns();
      }
    }
  }

  Future<void> initialize() async {
    cubit.showLoading();
    await cubit.initialize();
    cubit.hideLoading();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      title: TextField(
        controller: _controller,
        focusNode: _focusNode,
        style: Theme.of(context).textTheme.bodySmall,
        onSubmitted: (value) async {
          await _getCampaigns(value);
        },
        onChanged: (value) {
          cubit.updateSearchText(value);

          _debounceTimer?.cancel();

          if (value.isEmpty) {
            cubit.clearResults();
          } else {
            _debounceTimer = Timer(const Duration(seconds: 1), () {
              cubit.findCampaignSearchConditions(value);
            });
          }
        },
        decoration: InputDecoration(
            hintText: 'Search campaign',
            hintStyle: Theme.of(context).textTheme.bodySmall!.copyWith(color: const Color(0xFF767676)),
            border: InputBorder.none,
            focusedBorder: InputBorder.none,
            enabledBorder: InputBorder.none,
            suffixIcon: BlocBuilder<CampaignSearchCubit, CampaignSearchState>(
              builder: (context, state) {
                if (state.isSearchingByKeyword) {
                  return Container(
                    width: 24.r,
                    height: 24.r,
                    padding: const EdgeInsets.all(6),
                    child: const CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFEF6507)),
                    ),
                  );
                } else if (_controller.text.isNotEmpty) {
                  return IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () async {
                      _controller.clear();
                      cubit.resetToSearchHistory();
                    },
                  );
                } else {
                  return const SizedBox.shrink();
                }
              },
            )),
      ),
      bottom: PreferredSize(
        preferredSize: Size.fromHeight(1.r),
        child: Divider(
          height: 1.r,
          color: const Color(0xFFE7E7E7),
        ),
      ),
    );
  }

  Future<void> _getCampaigns(String value) async {
    cubit.showLoading();

    cubit.updateSearchText(value);

    if (value.isNotEmpty) {
      await cubit.findCampaignSearchConditions(value);
    } else {
      await cubit.clearResults();
    }

    cubit.hideLoading();
  }

  Widget _buildBody() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(),
        Expanded(
          child: _buildContents(),
        ),
      ],
    );
  }

  Widget _buildCampaignSearchConditionResults(List<DefaultCampaignSummary> conditions) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.r, vertical: 8.r),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: conditions
              .map((item) {
                List<String> names = splitString(item.name ?? '-', cubit.state.searchText);
                return GestureDetector(
                  onTap: () async {
                    await cubit.setSearchTextHistory(item.name ?? '-');

                    if (!mounted) return;

                    AffiliationStatus? status = await showCampaignDetailModal(context, item.id);
                    if (status != null && mounted) {
                      Modular.to.pop(status);
                    }
                  },
                  child: SizedBox(
                    height: 40,
                    child: Row(
                      children: [
                        AspectRatio(
                          aspectRatio: 1,
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8.r),
                              border: Border.all(color: const Color(0xFFE7E7E7), width: 1.r),
                            ),
                            child: CachedImageWithPlaceholder(imageUrl: item.imageUrl ?? ''),
                          ),
                        ),
                        SizedBox(width: 4.r),
                        Expanded(
                          child: RichText(
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              text: TextSpan(
                                  style: Theme.of(context).textTheme.labelLarge,
                                  children: names
                                      .map((textItem) => TextSpan(
                                          text: textItem,
                                          style: textItem.toLowerCase() == cubit.state.searchText.toLowerCase()
                                              ? Theme.of(context)
                                                  .textTheme
                                                  .labelLarge!
                                                  .copyWith(fontWeight: FontWeight.bold)
                                              : null))
                                      .toList())),
                        ),
                      ],
                    ),
                  ),
                );
              })
              .map((widget) => Column(
                    children: [
                      widget,
                      SizedBox(height: 8.r),
                    ],
                  ))
              .toList()
            ..removeLast(),
        ),
      ),
    );
  }

  List<String> splitString(String s, String delimiter) {
    String sLower = s.toLowerCase();
    String delimiterLower = delimiter.toLowerCase();

    List<String> parts = [];
    int start = 0;

    while (true) {
      int index = sLower.indexOf(delimiterLower, start);
      if (index == -1) {
        parts.add(s.substring(start));
        break;
      }
      parts.add(s.substring(start, index));
      parts.add(s.substring(index, index + delimiter.length));
      start = index + delimiter.length;
    }

    return parts;
  }

  Widget _buildContents() {
    return BlocBuilder<CampaignSearchCubit, CampaignSearchState>(
      bloc: cubit,
      builder: (context, state) {
        if (state.isDataLoaded) {
          return state.campaignResults.isNotEmpty ? _buildExistingCampaignContents(state) : const EmptyStateWidget();
        }

        if (state.searchText.isEmpty) {
          return _buildSearchHistories(state.searchTextHistories);
        }

        if (state.searchText.isNotEmpty && state.textSearchResults.isNotEmpty) {
          return _buildCampaignSearchConditionResults(state.textSearchResults);
        }

        if (state.searchText.isNotEmpty &&
            state.textSearchResults.isEmpty &&
            !state.isSearchingByKeyword &&
            state.hasSearched) {
          return const EmptyStateWidget();
        }

        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildExistingCampaignContents(CampaignSearchState state) {
    return ListView.builder(
      controller: _scrollController,
      padding: EdgeInsets.symmetric(horizontal: 16.r, vertical: 8.r),
      itemCount: state.campaignResults.length + ((state.isLoadingMore || state.hasMoreData) ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == state.campaignResults.length) {
          if (state.isLoadingMore) {
            return Padding(
              padding: EdgeInsets.symmetric(vertical: 16.r),
              child: const Center(
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFFFB522)),
                ),
              ),
            );
          } else {
            return const SizedBox.shrink();
          }
        }
        final campaign = state.campaignResults[index];
        return Padding(
          padding: EdgeInsets.only(bottom: 8.r),
          child: _buildCampaignItem(campaign),
        );
      },
    );
  }

  Future<List<String>> _filterViewedCampaigns(List<String> histories) async {
    final results = await Future.wait(
      histories.map((item) async {
        final campaign = await cubit.findCampaignByName(item);
        return campaign != null ? item : null;
      }),
    );

    return results.where((item) => item != null).cast<String>().toList();
  }

  Widget _buildSearchHistories(List<String> histories) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.r, vertical: 8.r),
      child: FutureBuilder<List<String>>(
        future: _filterViewedCampaigns(histories),
        builder: (context, snapshot) {
          if (!snapshot.hasData) {
            return const SizedBox.shrink();
          }

          final viewedCampaigns = snapshot.data!;

          return viewedCampaigns.isNotEmpty
              ? SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text('Viewed campaigns',
                              style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.w500)),
                          TextButton(
                            onPressed: () {
                              cubit.clearSearchTextHistories();
                            },
                            child: Text('Clear',
                                style: Theme.of(context)
                                    .textTheme
                                    .labelLarge!
                                    .copyWith(fontWeight: FontWeight.w500, color: const Color(0xFFEF6507))),
                          ),
                        ],
                      ),
                      ...viewedCampaigns.map((item) {
                        return Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: GestureDetector(
                                onTap: () async {
                                  final campaign = await cubit.findCampaignByName(item);

                                  if (campaign == null) {
                                    _controller.text = item;
                                    cubit.updateSearchText(item);
                                    await _getCampaigns(item);
                                    return;
                                  }

                                  if (!mounted) return;

                                  AffiliationStatus? status =
                                      await showCampaignDetailModal(context, campaign.id, initialHeight: 1.0);
                                  if (status != null && mounted) {
                                    Modular.to.pop(status);
                                  }
                                },
                                child: Padding(
                                  padding: EdgeInsets.only(top: 8.r, bottom: 8.r),
                                  child: Text(
                                    item,
                                    style:
                                        Theme.of(context).textTheme.labelLarge!.copyWith(fontWeight: FontWeight.w500),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        );
                      }),
                    ],
                  ),
                )
              : Text('No viewed campaigns',
                  style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.w500));
        },
      ),
    );
  }

  Widget _buildCampaignItem(DefaultCampaignSummary campaign) {
    return GestureDetector(
      onTap: () async {
        await cubit.setSearchTextHistory(campaign.name ?? '');

        if (!mounted) return;

        AffiliationStatus? status = await showCampaignDetailModal(context, campaign.id);
        if (status != null && mounted) {
          Modular.to.pop(status);
        }
      },
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12.r),
        child: Container(
          color: Colors.grey[200],
          height: 80.r,
          child: Row(
            children: [
              AspectRatio(
                  aspectRatio: 1,
                  child: Container(
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12.r),
                        color: Colors.white,
                        border: Border.all(color: Colors.grey[300]!, width: 1.r)),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12.r),
                      child: CachedImageWithPlaceholder(
                        imageUrl: campaign.imageUrl ?? '',
                        fit: BoxFit.contain,
                      ),
                    ),
                  )),
              Expanded(
                child: Padding(
                  padding: EdgeInsets.all(16.r),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Flexible(
                            child: Text(
                              campaign.name ?? '',
                              style: Theme.of(context).textTheme.labelLarge!.copyWith(fontWeight: FontWeight.bold),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                          const SizedBox(width: 8),
                          campaign.highestRewardSummaries.isNotEmpty
                              ? Container(
                                  padding: EdgeInsets.symmetric(horizontal: 4.r, vertical: 2.r),
                                  decoration:
                                      BoxDecoration(borderRadius: BorderRadius.circular(6.r), color: Colors.white),
                                  child: _buildRewardValue(
                                      context: context,
                                      reward: campaign.highestRewardSummaries.first.reward,
                                      type: campaign.highestRewardSummaries.first.type,
                                      currency: cubit.state.currency),
                                )
                              : const SizedBox.shrink()
                        ],
                      ),
                      SizedBox(height: 4.r),
                      Text(
                        campaign.category ?? '',
                        style: Theme.of(context).textTheme.labelMedium!.copyWith(color: const Color(0xFF767676)),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      )
                    ],
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: EdgeInsets.all(8.r),
      child: BlocBuilder<CampaignSearchCubit, CampaignSearchState>(builder: (context, state) {
        return SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              _buildHeaderContents(_categoryTitle(state), () {
                _showCategoryModalBottomSheet();
              }),
              SizedBox(width: 8.r),
              _buildHeaderContents(_typeTitle(state), () {
                _showTypeModalBottomSheet();
              }),
              SizedBox(width: 8.r),
              _buildClearAllButton(state),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildClearAllButton(CampaignSearchState state) {
    return _showClearAll(state)
        ? TextButton(
            onPressed: () {
              cubit.clearAll();
            },
            child: Text(
              'Clear all',
              style: Theme.of(context)
                  .textTheme
                  .labelLarge!
                  .copyWith(fontWeight: FontWeight.w500, color: const Color(0xFFEF6507)),
            ))
        : const SizedBox.shrink();
  }

  bool _showClearAll(CampaignSearchState state) {
    return state.isCategoryChecked.where((item) => item).isNotEmpty ||
        state.isTypeChecked.where((item) => item).isNotEmpty;
  }

  Widget _buildHeaderContents(String title, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(6.r),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(8.r)),
          border: Border.all(color: const Color(0xFFE7E7E7)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.labelLarge!.copyWith(fontWeight: FontWeight.w500),
              overflow: TextOverflow.ellipsis,
            ),
            Icon(
              Icons.arrow_drop_down,
              size: 18.r,
            )
          ],
        ),
      ),
    );
  }

  void _showCategoryModalBottomSheet() {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.white,
        builder: (BuildContext context) {
          return ClipRRect(
              borderRadius: BorderRadius.circular(12.r),
              child: Column(
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 8.r, vertical: 12.r),
                    child: Row(
                      children: [
                        IconButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          icon: Icon(
                            Icons.close,
                            size: 20.r,
                          ),
                        ),
                        SizedBox(width: 12.r),
                        Text(
                          'Category',
                          style: Theme.of(context).textTheme.titleSmall!.copyWith(fontWeight: FontWeight.w500),
                        )
                      ],
                    ),
                  ),
                  BlocBuilder<CampaignSearchCubit, CampaignSearchState>(
                      bloc: cubit,
                      builder: (context, state) {
                        return Expanded(
                          child: SingleChildScrollView(
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: Iterable.generate(state.categories.length, (i) {
                                Category category = state.categories[i];
                                return SizedBox(
                                  height: 40.r,
                                  child: CheckboxListTile(
                                      contentPadding: EdgeInsets.symmetric(horizontal: 16.r),
                                      title: Text(category.name,
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodySmall!
                                              .copyWith(fontWeight: FontWeight.w500)),
                                      value: state.isCategoryChecked[i],
                                      activeColor: const Color(0xFFFFB522),
                                      onChanged: (value) {
                                        cubit.onCategorySelectionChanged(i, value ?? false);
                                      }),
                                );
                              }).toList(),
                            ),
                          ),
                        );
                      }),
                  Padding(
                    padding: EdgeInsets.all(16.r),
                    child: Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                              style: Theme.of(context).elevatedButtonTheme.style!.copyWith(
                                    backgroundColor: WidgetStateProperty.all(Colors.white),
                                    side: WidgetStateProperty.all(BorderSide(
                                      color: const Color(0xFFAAAAAA),
                                      width: 1.0.r,
                                    )),
                                  ),
                              onPressed: () {
                                cubit.resetCategoryChecked();
                              },
                              child: Text(
                                'Clear',
                                style: Theme.of(context).textTheme.labelLarge!.copyWith(fontWeight: FontWeight.w500),
                              )),
                        ),
                        SizedBox(width: 16.r),
                        Expanded(
                          child: ElevatedButton(
                              onPressed: _handleFilterApply,
                              child: Text('Apply',
                                  style: Theme.of(context)
                                      .textTheme
                                      .labelLarge!
                                      .copyWith(fontWeight: FontWeight.w500, color: Colors.white))),
                        ),
                      ],
                    ),
                  )
                ],
              ));
        });
  }

  void _showTypeModalBottomSheet() {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.white,
        builder: (BuildContext context) {
          return ClipRRect(
              borderRadius: BorderRadius.circular(12.r),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 8.r, vertical: 12.r),
                    child: Row(
                      children: [
                        IconButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          icon: Icon(
                            Icons.close,
                            size: 20.r,
                          ),
                        ),
                        SizedBox(width: 12.r),
                        Text(
                          'Type',
                          style: Theme.of(context).textTheme.titleSmall!.copyWith(fontWeight: FontWeight.w500),
                        )
                      ],
                    ),
                  ),
                  BlocBuilder<CampaignSearchCubit, CampaignSearchState>(
                      bloc: cubit,
                      builder: (context, state) {
                        return SingleChildScrollView(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: Iterable.generate(state.types.length, (i) {
                              CampaignType type = state.types[i];
                              return SizedBox(
                                height: 40.r,
                                child: CheckboxListTile(
                                    contentPadding: EdgeInsets.symmetric(horizontal: 16.r),
                                    title: Text(type.value,
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodySmall!
                                            .copyWith(fontWeight: FontWeight.w500)),
                                    value: state.isTypeChecked[i],
                                    activeColor: const Color(0xFFFFB522),
                                    onChanged: (value) {
                                      cubit.onTypeSelectionChanged(i, value ?? false);
                                    }),
                              );
                            }).toList(),
                          ),
                        );
                      }),
                  Padding(
                    padding: EdgeInsets.all(16.r),
                    child: Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                              style: Theme.of(context).elevatedButtonTheme.style!.copyWith(
                                    backgroundColor: WidgetStateProperty.all(Colors.white),
                                    side: WidgetStateProperty.all(BorderSide(
                                      color: const Color(0xFFAAAAAA),
                                      width: 1.0.r,
                                    )),
                                  ),
                              onPressed: () {
                                cubit.resetTypeChecked();
                              },
                              child: Text(
                                'Clear',
                                style: Theme.of(context).textTheme.labelLarge!.copyWith(fontWeight: FontWeight.w500),
                              )),
                        ),
                        SizedBox(width: 16.r),
                        Expanded(
                          child: ElevatedButton(
                              onPressed: _handleFilterApply,
                              child: Text('Apply',
                                  style: Theme.of(context)
                                      .textTheme
                                      .labelLarge!
                                      .copyWith(fontWeight: FontWeight.w500, color: Colors.white))),
                        ),
                      ],
                    ),
                  )
                ],
              ));
        });
  }

  String _categoryTitle(CampaignSearchState state) {
    if (state.isCategoryChecked.any((checked) => checked)) {
      int size = state.isCategoryChecked.where((checked) => checked).length;
      for (int i = 0; i < state.isCategoryChecked.length; i++) {
        if (state.isCategoryChecked[i]) {
          return '${state.categories[i].name}${size > 1 ? ' +${size - 1}' : ''}';
        }
      }
    }
    return 'Category';
  }

  String _typeTitle(CampaignSearchState state) {
    if (state.isTypeChecked.any((checked) => checked)) {
      int size = state.isTypeChecked.where((checked) => checked).length;
      for (int i = 0; i < state.isTypeChecked.length; i++) {
        if (state.isTypeChecked[i]) {
          return '${state.types[i].name}${size > 1 ? ' +${size - 1}' : ''}';
        }
      }
    }
    return 'Type';
  }

  Future<void> _handleFilterApply() async {
    Navigator.of(context).pop();

    _controller.text = '';

    await cubit.applyFilters();
  }

  Widget _buildRewardValue({
    required BuildContext context,
    required int reward,
    required String type,
    required String currency,
  }) {
    final String displayValue = type == RewardType.CPA_SALES.name
        ? '$reward%'
        : (type == RewardType.CPA_FIXED.name || type == RewardType.CPC.name)
            ? reward.toPrice(currency)
            : reward.toString();

    return Text(
      displayValue,
      style: Theme.of(context).textTheme.labelMedium!.copyWith(color: const Color(0xFF15793E)),
    );
  }
}
