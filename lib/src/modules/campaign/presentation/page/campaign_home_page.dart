import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/account/presentation/widget/confirmation_buttons.dart';
import 'package:koc_app/src/modules/campaign/cubit/campaign_home_cubit.dart';
import 'package:koc_app/src/modules/campaign/cubit/campaign_home_state.dart';
import 'package:koc_app/src/modules/campaign/cubit/campaign_list_view_cubit.dart';
import 'package:koc_app/src/modules/campaign/cubit/campaign_list_view_state.dart';
import 'package:koc_app/src/modules/campaign/data/models/campaign.dart';
import 'package:koc_app/src/modules/campaign/presentation/mixin/campaign_detail_mixin.dart';
import 'package:koc_app/src/modules/campaign/presentation/widget/campaign_grid_item.dart';
import 'package:koc_app/src/modules/campaign/presentation/widget/campaign_summary_horizontal_view.dart';
import 'package:koc_app/src/modules/survey/data/models/survey_info.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/mixin/common_mixin.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';
import 'package:koc_app/src/shared/widgets/common_tab.dart';
import 'package:koc_app/src/shared/widgets/pull_to_refresh_wrapper.dart';

import '../widget/campaign_list_item.dart';

class CampaignHomePage extends StatefulWidget {
  const CampaignHomePage({super.key});

  @override
  State<CampaignHomePage> createState() => _CampaignHomePageState();
}

class _CampaignHomePageState extends BasePageState<CampaignHomePage, CampaignHomeCubit>
    with SingleTickerProviderStateMixin, CampaignDetailMixin, CommonMixin {
  late TabController _tabController;
  static const double loadMoreThreshold = 0.9;

  late final CampaignListViewCubit _campaignListViewCubit = Modular.get<CampaignListViewCubit>();

  final Map<int, ScrollController> _scrollControllers = {};

  int _currentDisplayTab = 0;
  bool _showPassionateValidMessage = false;

  @override
  void initState() {
    _initializeTabController();
    _initializeScrollControllers();
    _initData();
    super.initState();
  }

  void _initializeScrollControllers() {
    for (int i = 0; i < CampaignTabIndex.values.length; i++) {
      _scrollControllers[i] = ScrollController()..addListener(() => _scrollListener(i));
    }
  }

  void _scrollListener(int tabIndex) {
    if (tabIndex != _currentDisplayTab) return;

    final controller = _scrollControllers[tabIndex];
    if (controller != null && controller.hasClients) {
      final scrollThreshold = loadMoreThreshold * controller.position.maxScrollExtent;
      if (controller.position.pixels >= scrollThreshold && !cubit.state.isLoadingMore && cubit.isHasMore(tabIndex)) {
        cubit.fetchMoreCampaigns(tabIndex);
      }
    }
  }

  Future<void> _initData() async {
    cubit.startSiteSwitching();
    cubit.showLoading();
    await cubit.fetchHomeCampaigns();
    await cubit.getPassionateInfo();
    if (cubit.state.passionateInfo == const PassionateInfo()) {
      _showPassionateInfoSurvey();
    }

    cubit.hideLoading();
    cubit.endSiteSwitching();

    if (_tabController.index == 0 && cubit.state.availableCampaigns.isEmpty && cubit.isHasMore(0)) {
      cubit.fetchMoreCampaigns(0);
    }

    _checkAndDisplayNotification();
  }

  void _checkAndDisplayNotification() {
    Future.microtask(() {
      if (mounted) {
        String notificationMessage = cubit.state.notificationMessage;
        if (notificationMessage.isNotEmpty) {
          context.showSnackBar('Thank you for applying!\n$notificationMessage');
          cubit.clearNotificationMessage();
        }
      }
    });
  }

  void _initializeTabController() {
    _tabController = TabController(length: CampaignTabIndex.values.length, vsync: this);
    _tabController.index = cubit.state.tabIndex;
    _currentDisplayTab = _tabController.index;

    _tabController.addListener(() {
      if (_tabController.indexIsChanging || _tabController.index != cubit.state.tabIndex) {
        _currentDisplayTab = _tabController.index;
        cubit.changeTab(_tabController.index);

        if (_scrollControllers.containsKey(_tabController.index)) {
          final controller = _scrollControllers[_tabController.index];
          if (controller != null && controller.hasClients) {
            controller.animateTo(
              0,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
            );
          }
        }
      }
    });
  }

  void _showPassionateInfoSurvey() async {
    bool? result = await showDialog<bool?>(
        useSafeArea: true,
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            backgroundColor: Colors.white,
            title: Text('What are you passionate about?', style: context.textBodyMedium()),
            content: _buildPassionateInfoSurveyContent(),
            titlePadding: EdgeInsets.only(left: 16.r, top: 16.r, right: 16.r),
            contentPadding: EdgeInsets.all(16.r),
            actions: [_buildButtons()],
          );
        });

    if (true != result) {
      cubit.clearPassionateInfo();
    }
  }

  Widget _buildButtons() {
    return BlocBuilder<CampaignHomeCubit, CampaignHomeState>(
      bloc: cubit,
      builder: (_, state) {
        return ConfirmationButtons(
          isValid: state.passionateInfo.selectedPassionateItems.length >= 3,
          btnName: 'Add',
          onTap: () {
            cubit.savePassionateInfo();
            Navigator.of(context).pop(true);
          },
          showCancelButton: true,
          alignment: MainAxisAlignment.end,
          cancelButtonName: 'Do it later',
        );
      },
    );
  }

  Widget _buildPassionateInfoSurveyContent() {
    return BlocBuilder<CampaignHomeCubit, CampaignHomeState>(
      bloc: cubit,
      builder: (_, state) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          spacing: 16.r,
          children: [
            Text(
              'Select the fields that you interested so we can suggest you a suitable campaign to promote.',
              style: context.textLabelLarge(fontWeight: FontWeight.w500),
            ),
            if (_showPassionateValidMessage && state.passionateInfo.selectedPassionateItems.length < 3)
              Text(
                'Please select at least 3 for the best experience.',
                style: context.textLabelLarge(fontWeight: FontWeight.w500, color: Colors.red),
              ),
            SizedBox(
              width: double.infinity,
              child: Wrap(
                  spacing: 12.r,
                  runSpacing: 12.r,
                  children: PassionateItem.values.map(
                    (e) {
                      return GestureDetector(
                        onTap: () {
                          setState(() {
                            _showPassionateValidMessage = true;
                          });
                          cubit.selectPassionateInfo(e);
                        },
                        child: Container(
                          padding: EdgeInsets.symmetric(horizontal: 12.r, vertical: 8.r),
                          decoration: BoxDecoration(
                            color: cubit.state.passionateInfo.selectedPassionateItems.contains(e)
                                ? const Color(0xFFFFB522).withValues(alpha: 0.3)
                                : Colors.white,
                            borderRadius: BorderRadius.circular(8.r),
                            border: Border.all(
                              color: cubit.state.passionateInfo.selectedPassionateItems.contains(e)
                                  ? const Color(0xFFFFB522)
                                  : const Color(0xFFD8D8D8),
                            ),
                          ),
                          child: Row(
                            spacing: 8.r,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                e.value,
                                style: context.textLabelMedium(fontWeight: FontWeight.w500).copyWith(letterSpacing: 0),
                              ),
                              Icon(
                                e.icon,
                                size: 12.r,
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ).toList()),
            ),
          ],
        );
      },
    );
  }

  @override
  void dispose() {
    for (var controller in _scrollControllers.values) {
      controller.dispose();
    }
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: buildSiteSelectionTitle(context, 'Campaign', onTap: _initData),
        showNotificationAction: true,
        showFindCampaignAction: true,
        showBottomDivider: false,
        onSearchTap: () async {
          final result = await Modular.to.pushNamed('/campaign/search');
          if (result is AffiliationStatus) {
            if (mounted && context.mounted) {
              await moveCampaignTab(result, context: context, navigateToTab: false);
            } else {
              await moveCampaignTab(result, navigateToTab: false);
            }

            _checkAndDisplayNotification();
          }
        },
      ),
      body: Column(
        spacing: 10.r,
        children: [
          _buildTabBar(),
          _buildCampaignContent(),
        ],
      ),
    );
  }

  Widget _buildCampaignContent() {
    return BlocBuilder<CampaignHomeCubit, CampaignHomeState>(
      bloc: cubit,
      builder: (context, state) {
        if (_tabController.index != state.tabIndex && !_tabController.indexIsChanging) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted && !_tabController.indexIsChanging) {
              _tabController.animateTo(state.tabIndex);
            }
          });
        }

        return Expanded(
          child: TabBarView(
            controller: _tabController,
            physics: const NeverScrollableScrollPhysics(),
            children: [
              _buildAvailableTab(state),
              _buildTabContent(
                  state.waitingCampaigns, state.campaignSummariesCount.waitingCount, CampaignTabIndex.WAITING.index),
              _buildTabContent(state.affiliatedCampaigns, state.campaignSummariesCount.affiliatedCount,
                  CampaignTabIndex.AFFILIATED.index),
              _buildTabContent(
                  state.pausedCampaigns, state.campaignSummariesCount.pausedCount, CampaignTabIndex.PAUSED.index),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAvailableTab(CampaignHomeState state) {
    return PullToRefreshWrapper(
      onRefresh: () => cubit.pullToRefresh(),
      child: SingleChildScrollView(
        controller: _scrollControllers[CampaignTabIndex.AVAILABLE.index],
        key: const PageStorageKey('available_tab'),
        physics: const AlwaysScrollableScrollPhysics(),
        child: Padding(
          padding: EdgeInsets.only(left: 16.r, right: 16.r),
          child: Column(
            children: [
              if (state.recommendCampaigns.isNotEmpty)
                CampaignSummaryHorizontalView(
                  CampaignViewType.RECOMMENDED.name.toTitleCase(),
                  state.currency,
                  null,
                  state.recommendCampaigns,
                  spacerSize: 10,
                  cardColor: const Color(0xFFFFB522).withValues(alpha: 0.3),
                ),
              if (state.availableCampaigns.isNotEmpty) SizedBox(height: 18.r),
              _buildView(state.availableCampaigns, state.campaignSummariesCount.availableCount, 0),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTabContent(List<DefaultCampaignSummary> campaigns, int totalCount, int tabIndex) {
    return PullToRefreshWrapper(
      onRefresh: () => cubit.pullToRefresh(),
      child: SingleChildScrollView(
        controller: _scrollControllers[tabIndex],
        key: PageStorageKey('tab_$tabIndex'),
        physics: const AlwaysScrollableScrollPhysics(),
        child: Padding(
          padding: EdgeInsets.only(left: 16.r, right: 16.r),
          child: _buildView(campaigns, totalCount, tabIndex),
        ),
      ),
    );
  }

  /// Builds a list view of campaigns (unused but kept for future use)
  Widget _buildListView(List<DefaultCampaignSummary> campaigns) {
    return Column(
      spacing: 8.r,
      children: campaigns
          .map((campaign) => CampaignListItem(campaign, cubit.state.currency, () async {
                await showCampaignDetailAndChangeTab(context, campaign.id);
              }))
          .toList(),
    );
  }

  Widget _buildGridView(List<DefaultCampaignSummary> campaigns) {
    return Wrap(
      spacing: 0.12.sw - 32.r,
      runSpacing: 10.r,
      children: campaigns
          .map((campaign) => CampaignGridItem(campaign, cubit.state.currency, 0.44.sw, () async {
                await showCampaignDetailAndChangeTab(context, campaign.id,
                    highestRewardSummaries: campaign.highestRewardSummaries);
              }))
          .toList(),
    );
  }

  Widget _buildView(List<DefaultCampaignSummary> campaigns, int totalCount, int tabIndex) {
    final key = ValueKey('content_$tabIndex');
    return BlocBuilder<CampaignListViewCubit, CampaignListViewState>(
      bloc: _campaignListViewCubit,
      builder: (_, state) {
        final shouldShowLoading = cubit.state.isLoading && !cubit.state.isPullToRefresh && !cubit.state.isSiteSwitching;

        if (shouldShowLoading && campaigns.isNotEmpty) {
          return SizedBox(
            height: 0.7.sh,
            child: const Center(
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: Color(0xFFFFB522),
              ),
            ),
          );
        }

        if ((totalCount == 0 || campaigns.isEmpty) && !cubit.state.isLoading && !cubit.state.isSiteSwitching) {
          return SizedBox(
            height: 0.7.sh,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.campaign_outlined,
                    size: 42.r,
                    color: Colors.grey[400],
                  ),
                  SizedBox(height: 16.r),
                  Text(
                    'No campaigns available',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[400],
                        ),
                  ),
                ],
              ),
            ),
          );
        }

        return Column(
          key: key,
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 8.r,
          children: [
            Padding(
              padding: EdgeInsets.only(
                left: 4.r,
                top: 8.r,
              ),
            ),
            _buildGridView(campaigns),
            if (cubit.state.isLoadingMore &&
                campaigns.isNotEmpty &&
                !cubit.state.isPullToRefresh &&
                !cubit.state.isSiteSwitching)
              Padding(
                padding: EdgeInsets.symmetric(vertical: 16.r),
                child: const Center(
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: Color(0xFFFFB522),
                  ),
                ),
              ),
            SizedBox(height: 16.r),
          ],
        );
      },
    );
  }

  void switchToTab(int tabIndex) {
    if (mounted && !_tabController.indexIsChanging && _tabController.index != tabIndex) {
      _tabController.animateTo(tabIndex);
    }
  }

  Future<void> showCampaignDetailAndChangeTab(BuildContext context, int campaignId,
      {List<HighestRewardSummary>? highestRewardSummaries}) async {
    AffiliationStatus? status = await showCampaignDetailModal(context, campaignId,
        initialHeight: 0.5, highestRewardSummaries: highestRewardSummaries);
    if (status != null) {
      if (mounted && context.mounted) {
        await moveCampaignTab(status, context: context, navigateToTab: false);
      } else {
        await moveCampaignTab(status, navigateToTab: false);
      }

      _checkAndDisplayNotification();
    }
  }

  Widget _buildTabBar() {
    return BlocBuilder<CampaignHomeCubit, CampaignHomeState>(
        bloc: cubit,
        builder: (context, state) {
          final availableCount = state.campaignSummariesCount.availableCount;
          final waitingCount = state.campaignSummariesCount.waitingCount;
          final affiliatedCount = state.campaignSummariesCount.affiliatedCount;
          final pausedCount = state.campaignSummariesCount.pausedCount;
          return TabBar(
            controller: _tabController,
            isScrollable: true,
            labelStyle: Theme.of(context).textTheme.labelLarge,
            tabs: [
              availableCount > 0
                  ? CommonTab(CampaignTabIndex.AVAILABLE.label, count: availableCount)
                  : const SizedBox.shrink(),
              waitingCount > 0
                  ? CommonTab(CampaignTabIndex.WAITING.label, count: waitingCount)
                  : const SizedBox.shrink(),
              affiliatedCount > 0
                  ? CommonTab(CampaignTabIndex.AFFILIATED.label, count: affiliatedCount)
                  : const SizedBox.shrink(),
              pausedCount > 0 ? CommonTab(CampaignTabIndex.PAUSED.label, count: pausedCount) : const SizedBox.shrink(),
            ],
          );
        });
  }
}
