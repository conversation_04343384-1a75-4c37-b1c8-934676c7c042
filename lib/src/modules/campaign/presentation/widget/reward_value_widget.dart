import 'package:flutter/material.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/modules/campaign/data/models/campaign.dart';

class RewardValueWidget extends StatelessWidget {
  final num? reward;
  final String type;
  final String currency;
  final TextStyle? style;
  final List<DefaultReward>? rewards;

  const RewardValueWidget({
    super.key,
    this.reward,
    this.type = '',
    required this.currency,
    this.style,
    this.rewards,
  });

  static String formatRewardValue({
    num? reward,
    String type = '',
    required String currency,
    List<DefaultReward>? rewards,
  }) {
    num? displayReward = reward;
    String displayType = type;
    if (rewards != null && rewards.isNotEmpty) {
      DefaultReward? maxRewardReward =
          rewards.where((e) => e.reward != null).fold<DefaultReward?>(null, (prev, element) {
        if (prev == null) return element;
        return (element.reward! > prev.reward!) ? element : prev;
      });
      if (maxRewardReward != null) {
        displayReward = maxRewardReward.reward;
        displayType = maxRewardReward.type ?? '';
      }
    }
    if (displayReward == null) {
      return '-';
    }
    if (displayType == RewardType.CPA_SALES.name) {
      return '${displayReward.toStringAsFixed(0)}%';
    } else if (displayType == RewardType.CPA_FIXED.name || displayType == RewardType.CPC.name) {
      return displayReward.toInt().toPrice(currency);
    } else {
      return displayReward.toString();
    }
  }

  @override
  Widget build(BuildContext context) {
    final String displayValue = RewardValueWidget.formatRewardValue(
      reward: reward,
      type: type,
      currency: currency,
      rewards: rewards,
    );
    return Text(
      displayValue,
      style: style ?? Theme.of(context).textTheme.labelMedium!.copyWith(color: const Color(0xFF15793E)),
    );
  }
}
