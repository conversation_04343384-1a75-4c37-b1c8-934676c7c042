import 'package:koc_app/src/shared/widgets/cached_image_with_placeholder.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/modules/campaign/data/models/campaign.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/modules/campaign/presentation/widget/reward_value_widget.dart';

class CampaignListItem extends StatelessWidget {
  final DefaultCampaignSummary campaign;
  final String currency;
  final VoidCallback onTap;
  const CampaignListItem(this.campaign, this.currency, this.onTap, {super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        color: Colors.grey[200],
        height: 80.r,
        width: double.infinity,
        child: Row(
          children: [
            _buildImage(),
            Expanded(
              child: Padding(
                padding: EdgeInsets.all(16.r),
                child: _buildContent(context),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildImage() {
    return AspectRatio(
      aspectRatio: 1.0,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(color: Colors.grey[300]!, width: 1.r),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12.r),
          child: CachedImageWithPlaceholder(
            imageUrl: campaign.imageUrl ?? '',
            fit: BoxFit.contain,
          ),
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                campaign.name ?? '',
                style: Theme.of(context).textTheme.labelLarge!.copyWith(fontWeight: FontWeight.bold),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (campaign.highestRewardSummaries.isNotEmpty) _buildRewardContainer(context),
          ],
        ),
        Text(
          campaign.category ?? '',
          style: Theme.of(context).textTheme.labelMedium!.copyWith(color: Colors.grey[500]),
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Widget _buildRewardContainer(BuildContext context) {
    return Container(
        alignment: Alignment.center,
        padding: EdgeInsets.only(left: 4.r, right: 4.r, top: 2.r, bottom: 0.r),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6.r),
          color: Colors.white,
        ),
        child: _buildRewardValue(
          context: context,
          reward: campaign.highestRewardSummaries.first.reward,
          type: campaign.highestRewardSummaries.first.type,
          currency: currency,
        ));
  }

  Widget _buildRewardValue({
    required BuildContext context,
    required int reward,
    required String type,
    required String currency,
  }) {
    final String displayValue = RewardValueWidget.formatRewardValue(
      reward: reward,
      type: type,
      currency: currency,
    );
    return Text(
      displayValue,
      style: Theme.of(context).textTheme.labelMedium!.copyWith(color: const Color(0xFF15793E)),
    );
  }
}
