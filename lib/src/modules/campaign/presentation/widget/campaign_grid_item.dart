import 'package:koc_app/src/shared/widgets/cached_image_with_placeholder.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/modules/campaign/data/models/campaign.dart';
import 'package:koc_app/src/modules/campaign/presentation/widget/reward_value_widget.dart';

class CampaignGridItem extends StatelessWidget {
  final DefaultCampaignSummary campaign;
  final String currency;
  final double width;
  final VoidCallback onTap;

  const CampaignGridItem(this.campaign, this.currency, this.width, this.onTap, {super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: width,
        height: 154.r,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.r),
          color: Colors.grey[200],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildImage(),
            _buildHeader(context),
            _buildCategory(context),
          ],
        ),
      ),
    );
  }

  Widget _buildImage() {
    return Container(
      width: width,
      height: 96.r,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
        border: Border.all(color: Colors.grey[300]!),
        color: Colors.white,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(10.r),
        child: Padding(
          padding: EdgeInsets.only(top: 8.r, bottom: 8.r),
          child: CachedImageWithPlaceholder(
            imageUrl: campaign.imageUrl ?? '',
            fit: BoxFit.contain,
            width: width,
            height: 96.r,
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 8.r, right: 8.r, top: 8.r, bottom: 4.r),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              campaign.name ?? '',
              style: Theme.of(context).textTheme.labelLarge!.copyWith(fontWeight: FontWeight.bold),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (campaign.highestRewardSummaries.isNotEmpty) _buildRewardContainer(context),
        ],
      ),
    );
  }

  Widget _buildRewardContainer(BuildContext context) {
    return Container(
        alignment: Alignment.center,
        padding: EdgeInsets.only(left: 4.r, right: 4.r, top: 2.r, bottom: 0.r),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6.r),
          color: Colors.white,
        ),
        child: _buildRewardValue(
          context: context,
          reward: campaign.highestRewardSummaries.first.reward,
          type: campaign.highestRewardSummaries.first.type,
          currency: currency,
        ));
  }

  Widget _buildCategory(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 8.r, right: 8.r),
      child: Text(
        campaign.category ?? '',
        style: Theme.of(context).textTheme.labelMedium!.copyWith(color: Colors.grey[500]),
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _buildRewardValue({
    required BuildContext context,
    required int reward,
    required String type,
    required String currency,
  }) {
    final String displayValue = RewardValueWidget.formatRewardValue(
      reward: reward,
      type: type,
      currency: currency,
    );
    return Text(
      displayValue,
      style: Theme.of(context).textTheme.labelMedium!.copyWith(color: const Color(0xFF15793E)),
    );
  }
}
