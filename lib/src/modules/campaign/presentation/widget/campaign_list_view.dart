import 'package:koc_app/src/shared/widgets/cached_image_with_placeholder.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/modules/campaign/data/models/campaign.dart';
import 'package:koc_app/src/shared/extensions.dart';

class CampaignListView extends StatelessWidget {
  final List<DefaultCampaignSummary> campaigns;
  const CampaignListView(this.campaigns, {super.key});

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: ListView.separated(
        separatorBuilder: (context, index) => Divider(height: 1.r, color: Colors.grey),
        itemCount: campaigns.length,
        itemBuilder: (context, index) {
          final campaign = campaigns[index];
          return InkWell(
            onTap: () {
              Modular.to.pushNamed('/campaign/details/${campaign.id}');
            },
            child: Container(
              padding: EdgeInsets.symmetric(
                vertical: 5.r,
              ),
              child: Row(
                children: [
                  SizedBox(
                    width: 120.r,
                    child: CachedImageWithPlaceholder(
                      imageUrl: campaign.imageUrl ?? '',
                      fit: BoxFit.cover,
                    ),
                  ),
                  SizedBox(width: 10.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          campaign.name ?? '',
                          style: TextStyle(fontSize: 16.r, fontWeight: FontWeight.bold),
                          overflow: TextOverflow.ellipsis,
                        ),
                        SizedBox(height: 5.h),
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.black,
                            borderRadius: BorderRadius.circular(5.r),
                          ),
                          child: Text(
                            " ${campaign.highestRewardSummaries.isNotEmpty && campaign.highestRewardSummaries[0] != null ? campaign.highestRewardSummaries[0].reward.toPercentage() : '0%'} reward ",
                            style: TextStyle(fontSize: 13.r, color: Colors.white),
                          ),
                        ),
                        Text(
                          DateFormat('MMM dd, yyyy').format(DateTime.now()),
                          style: TextStyle(fontSize: 13.r),
                        )
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
