// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'campaign_search_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CampaignSearchStateImpl _$$CampaignSearchStateImplFromJson(
        Map<String, dynamic> json) =>
    _$CampaignSearchStateImpl(
      categories: (json['categories'] as List<dynamic>?)
              ?.map((e) => Category.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      isCategoryChecked: (json['isCategoryChecked'] as List<dynamic>?)
              ?.map((e) => e as bool)
              .toList() ??
          const [],
      types: (json['types'] as List<dynamic>?)
              ?.map((e) => $enumDecode(_$CampaignTypeEnumMap, e))
              .toList() ??
          CampaignType.values,
      isTypeChecked: (json['isTypeChecked'] as List<dynamic>?)
              ?.map((e) => e as bool)
              .toList() ??
          const [],
      isDataLoaded: json['isDataLoaded'] as bool? ?? false,
      campaignResults: (json['campaignResults'] as List<dynamic>?)
              ?.map((e) =>
                  DefaultCampaignSummary.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      searchText: json['searchText'] as String? ?? '',
      searchTextHistories: (json['searchTextHistories'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      textSearchResults: (json['textSearchResults'] as List<dynamic>?)
              ?.map((e) =>
                  DefaultCampaignSummary.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      errorMessage: json['errorMessage'] as String? ?? '',
      isLoading: json['isLoading'] as bool? ?? false,
      isSearchingByKeyword: json['isSearchingByKeyword'] as bool? ?? false,
      currency: json['currency'] as String? ?? '',
      hasSearched: json['hasSearched'] as bool? ?? false,
      currentPage: (json['currentPage'] as num?)?.toInt() ?? 1,
      pageSize: (json['pageSize'] as num?)?.toInt() ?? 10,
      hasMoreData: json['hasMoreData'] as bool? ?? false,
      isLoadingMore: json['isLoadingMore'] as bool? ?? false,
    );

Map<String, dynamic> _$$CampaignSearchStateImplToJson(
        _$CampaignSearchStateImpl instance) =>
    <String, dynamic>{
      'categories': instance.categories,
      'isCategoryChecked': instance.isCategoryChecked,
      'types': instance.types.map((e) => _$CampaignTypeEnumMap[e]!).toList(),
      'isTypeChecked': instance.isTypeChecked,
      'isDataLoaded': instance.isDataLoaded,
      'campaignResults': instance.campaignResults,
      'searchText': instance.searchText,
      'searchTextHistories': instance.searchTextHistories,
      'textSearchResults': instance.textSearchResults,
      'errorMessage': instance.errorMessage,
      'isLoading': instance.isLoading,
      'isSearchingByKeyword': instance.isSearchingByKeyword,
      'currency': instance.currency,
      'hasSearched': instance.hasSearched,
      'currentPage': instance.currentPage,
      'pageSize': instance.pageSize,
      'hasMoreData': instance.hasMoreData,
      'isLoadingMore': instance.isLoadingMore,
    };

const _$CampaignTypeEnumMap = {
  CampaignType.CPA: 'CPA',
  CampaignType.CPC: 'CPC',
  CampaignType.CPL: 'CPL',
  CampaignType.CPS: 'CPS',
};
