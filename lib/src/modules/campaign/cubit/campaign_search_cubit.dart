import 'dart:developer';

import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/campaign/cubit/campaign_search_state.dart';
import 'package:koc_app/src/modules/campaign/data/models/campaign.dart';
import 'package:koc_app/src/modules/campaign/data/repository/campaign_repository.dart';
import 'package:koc_app/src/shared/services/shared_preferences_service.dart';
import 'package:koc_app/src/shared/utils/handle_error.dart';

class CampaignSearchCubit extends BaseCubit<CampaignSearchState> {
  final CampaignRepository _campaignRepository;
  final SharedPreferencesService _sharedPreferencesService;
  CampaignSearchCubit(this._campaignRepository, this._sharedPreferencesService) : super(CampaignSearchState());

  Future<void> initialize() async {
    final currency = (await _sharedPreferencesService.getPublisherCurrency())!;
    List<Category> categories = await _campaignRepository.getCategories();
    SharedPreferencesService sharedPreferencesService = Modular.get<SharedPreferencesService>();
    List<String> histories = await sharedPreferencesService.getCampaignSearchHistories();
    emit(state.copyWith(
        currency: currency,
        searchTextHistories: histories,
        categories: categories,
        isCategoryChecked: List<bool>.filled(categories.length, false),
        isTypeChecked: List<bool>.filled(CampaignType.values.length, false)));
  }

  Future<void> setSearchTextHistory(String keyword) async {
    SharedPreferencesService sharedPreferencesService = Modular.get<SharedPreferencesService>();
    List<String> histories = List.from(state.searchTextHistories);
    if (!histories.contains(keyword)) {
      histories.insert(0, keyword);
      await sharedPreferencesService.setCampaignSearchHistories(keyword);
    }
    emit(state.copyWith(searchTextHistories: histories));
  }

  Future<void> getSearchTextHistories() async {
    SharedPreferencesService sharedPreferencesService = Modular.get<SharedPreferencesService>();
    emit(state.copyWith(searchTextHistories: await sharedPreferencesService.getCampaignSearchHistories()));
  }

  Future<void> clearSearchTextHistories() async {
    SharedPreferencesService sharedPreferencesService = Modular.get<SharedPreferencesService>();
    await sharedPreferencesService.clearCampaignSearchHistories();
    emit(state.copyWith(searchTextHistories: []));
  }

  void resetCategoryChecked() {
    emit(state.copyWith(isCategoryChecked: List<bool>.filled(state.isCategoryChecked.length, false)));
  }

  void updateCategoryChecked(int index, bool value) {
    List<bool> isCategoryChecked = List.from(state.isCategoryChecked);
    isCategoryChecked[index] = value;
    emit(state.copyWith(isCategoryChecked: isCategoryChecked));
  }

  void resetTypeChecked() {
    emit(state.copyWith(isTypeChecked: List<bool>.filled(state.isTypeChecked.length, false)));
  }

  void updateSearchText(String searchText) {
    emit(state.copyWith(searchText: searchText));
  }

  void updateTypeChecked(int index, bool value) {
    List<bool> isTypeChecked = List.from(state.isTypeChecked);
    isTypeChecked[index] = value;
    emit(state.copyWith(isTypeChecked: isTypeChecked));
  }

  void clearAll() {
    emit(state.copyWith(
        isCategoryChecked: List<bool>.filled(state.isCategoryChecked.length, false),
        isTypeChecked: List<bool>.filled(state.isTypeChecked.length, false),
        searchText: '',
        isDataLoaded: false,
        campaignResults: [],
        textSearchResults: [],
        hasSearched: false,
        currentPage: 1,
        hasMoreData: false,
        isLoadingMore: false));
  }

  Future<void> findCampaignSearchConditions(String keyword) async {
    if (keyword.isEmpty) {
      emit(state.copyWith(textSearchResults: [], hasSearched: false));
      return;
    }

    try {
      emit(state.copyWith(isSearchingByKeyword: true, hasSearched: true));

      final siteId = await _sharedPreferencesService.getCurrentSiteId();
      if (siteId == null) {
        emit(state.copyWith(errorMessage: "Site ID not found", isSearchingByKeyword: false));
        return;
      }

      final filter = CampaignFilter(
        keyword: keyword,
      );

      final List<Future<List<DefaultCampaignSummary>>> campaignFutures = [
        _loadCampaignsByType(_campaignRepository.findAvailableCampaigns, siteId, filter),
        _loadCampaignsByType(_campaignRepository.findAffiliatedCampaigns, siteId, filter),
        _loadCampaignsByType(_campaignRepository.findWaitingCampaigns, siteId, filter),
        _loadCampaignsByType(_campaignRepository.findPausedCampaigns, siteId, filter),
      ];

      final results = await Future.wait(campaignFutures);

      final List<DefaultCampaignSummary> allCampaigns = [];
      for (var campaigns in results) {
        allCampaigns.addAll(campaigns);
      }

      final filteredCampaigns =
          allCampaigns.where((item) => item.name!.toLowerCase().contains(keyword.toLowerCase())).toList();

      emit(state.copyWith(
        textSearchResults: filteredCampaigns,
        isSearchingByKeyword: false,
        errorMessage: '',
        hasSearched: true,
      ));
    } catch (e) {
      handleError(e,
          (message) => emit(state.copyWith(errorMessage: message, isSearchingByKeyword: false, textSearchResults: [])));
    }
  }

  Future<void> searchByCategoryAndType(
      {List<int>? categoryIds, List<CampaignType>? types, bool isLoadMore = false}) async {
    try {
      if (isLoadMore) {
        emit(state.copyWith(isLoadingMore: true));
      } else {
        emit(state.copyWith(isLoading: true, currentPage: 1));
      }

      final siteId = await _sharedPreferencesService.getCurrentSiteId();
      if (siteId == null) {
        emit(state.copyWith(errorMessage: "Site ID not found", isLoading: false, isLoadingMore: false));
        return;
      }

      final List<int> selectedCategoryIds = categoryIds ?? _getSelectedCategoryValues();
      final List<CampaignType> selectedTypes = types ?? _getSelectedTypes();

      if (selectedCategoryIds.isEmpty && selectedTypes.isEmpty) {
        emit(state.copyWith(
          campaignResults: [],
          isDataLoaded: true,
          isLoading: false,
          hasMoreData: false,
          currentPage: 1,
        ));
        return;
      }

      final int currentPage = isLoadMore ? state.currentPage + 1 : 1;
      final filter = CampaignFilter(
        categoryIds: selectedCategoryIds,
        campaignTypes: selectedTypes,
        limit: state.pageSize,
        page: currentPage,
      );

      final List<Future<List<DefaultCampaignSummary>>> campaignFutures = [
        _loadCampaignsByType(_campaignRepository.findAvailableCampaigns, siteId, filter),
        _loadCampaignsByType(_campaignRepository.findAffiliatedCampaigns, siteId, filter),
        _loadCampaignsByType(_campaignRepository.findWaitingCampaigns, siteId, filter),
        _loadCampaignsByType(_campaignRepository.findPausedCampaigns, siteId, filter),
      ];

      final results = await Future.wait(campaignFutures);

      final List<DefaultCampaignSummary> allCampaigns = [];
      for (var campaigns in results) {
        allCampaigns.addAll(campaigns);
      }

      final List<DefaultCampaignSummary> updatedResults =
          isLoadMore ? [...state.campaignResults, ...allCampaigns] : allCampaigns;

      // Check if there's more data available
      final bool hasMoreData = allCampaigns.length >= state.pageSize;

      emit(state.copyWith(
        campaignResults: updatedResults,
        isDataLoaded: true,
        isLoading: false,
        isLoadingMore: false,
        errorMessage: '',
        currentPage: currentPage,
        hasMoreData: hasMoreData,
      ));
    } catch (e) {
      emit(state.copyWith(
        errorMessage: e.toString(),
        isLoading: false,
        isLoadingMore: false,
      ));
    }
  }

  Future<void> loadMoreCampaigns() async {
    if (!state.hasMoreData || state.isLoadingMore) {
      return;
    }

    final List<int> selectedCategoryIds = _getSelectedCategoryValues();
    final List<CampaignType> selectedTypes = _getSelectedTypes();

    if (selectedCategoryIds.isNotEmpty || selectedTypes.isNotEmpty) {
      await searchByCategoryAndType(categoryIds: selectedCategoryIds, types: selectedTypes, isLoadMore: true);
    }
  }

  List<int> _getSelectedCategoryValues() {
    final List<int> selectedCategories = [];
    for (int i = 0; i < state.isCategoryChecked.length; i++) {
      if (state.isCategoryChecked[i]) {
        selectedCategories.add(state.categories[i].value);
      }
    }
    return selectedCategories;
  }

  List<CampaignType> _getSelectedTypes() {
    final List<CampaignType> selectedTypes = [];
    for (int i = 0; i < state.isTypeChecked.length; i++) {
      if (state.isTypeChecked[i]) {
        selectedTypes.add(CampaignType.values[i]);
      }
    }
    return selectedTypes;
  }

  Future<void> getCampaigns(String searchText, List<Category> categories, List<CampaignType> types) async {
    try {
      if (searchText.isNotEmpty) {
        await findCampaignSearchConditions(searchText);
      } else if (categories.isNotEmpty || types.isNotEmpty) {
        final List<int> categoryIds = categories.map((c) => c.value).toList();
        await searchByCategoryAndType(categoryIds: categoryIds, types: types);
      } else {
        emit(state.copyWith(
          campaignResults: [],
          textSearchResults: [],
          isDataLoaded: true,
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        errorMessage: e.toString(),
        isLoading: false,
        isSearchingByKeyword: false,
      ));
    }
  }

  Future<List<DefaultCampaignSummary>> _loadCampaignsByType(
      Future<dynamic> Function(int, CampaignFilter) fetchFunction, int siteId, CampaignFilter filter) async {
    try {
      final result = await fetchFunction(siteId, filter);
      return _parseCampaignResult(result);
    } catch (e) {
      log("Error loading campaigns: ${e.toString()}");
      return [];
    }
  }

  List<DefaultCampaignSummary> _parseCampaignResult(dynamic result) {
    if (result == null) return [];
    return (result as List<dynamic>)
        .map((item) => DefaultCampaignSummary.fromJson(item as Map<String, dynamic>))
        .toList();
  }

  void onCategorySelectionChanged(int index, bool value) {
    final List<bool> updatedChecks = List.from(state.isCategoryChecked);
    updatedChecks[index] = value;

    emit(state.copyWith(
      isCategoryChecked: updatedChecks,
    ));
  }

  void onTypeSelectionChanged(int index, bool value) {
    final List<bool> updatedChecks = List.from(state.isTypeChecked);
    updatedChecks[index] = value;

    emit(state.copyWith(
      isTypeChecked: updatedChecks,
    ));
  }

  Future<void> clearResults() async {
    emit(state.copyWith(
      campaignResults: [],
      textSearchResults: [],
      isDataLoaded: false,
      isLoading: false,
      errorMessage: '',
      hasSearched: false,
      currentPage: 1,
      hasMoreData: false,
      isLoadingMore: false,
    ));
  }

  void resetToSearchHistory() {
    emit(state.copyWith(
      isDataLoaded: false,
      campaignResults: [],
      textSearchResults: [],
      searchText: '',
      hasSearched: false,
      currentPage: 1,
      hasMoreData: false,
      isLoadingMore: false,
    ));
  }

  Future<DefaultCampaignSummary?> findCampaignByName(String name) async {
    try {
      showLoading();
      final siteId = await _sharedPreferencesService.getCurrentSiteId();
      if (siteId == null) {
        return null;
      }
      final filter = CampaignFilter(
        keyword: name,
      );
      final campaignFutures = [
        _loadCampaignsByType(_campaignRepository.findAvailableCampaigns, siteId, filter),
        _loadCampaignsByType(_campaignRepository.findAffiliatedCampaigns, siteId, filter),
        _loadCampaignsByType(_campaignRepository.findWaitingCampaigns, siteId, filter),
        _loadCampaignsByType(_campaignRepository.findPausedCampaigns, siteId, filter),
      ];
      final results = await Future.wait(campaignFutures);
      final allCampaigns = <DefaultCampaignSummary>[];
      for (var campaigns in results) {
        allCampaigns.addAll(campaigns);
      }
      try {
        return allCampaigns.firstWhere((c) => c.name == name);
      } catch (_) {
        try {
          return allCampaigns.firstWhere(
            (c) => c.name!.toLowerCase().contains(name.toLowerCase()),
          );
        } catch (_) {
          return null;
        }
      }
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return null;
    } finally {
      hideLoading();
    }
  }

  Future<void> applyFilters() async {
    showLoading();
    updateSearchText('');
    final List<int> selectedCategoryValues = _getSelectedCategoryValues();
    final List<CampaignType> selectedTypes = _getSelectedTypes();
    if (selectedCategoryValues.isNotEmpty || selectedTypes.isNotEmpty) {
      await searchByCategoryAndType(categoryIds: selectedCategoryValues, types: selectedTypes);
    } else {
      await clearResults();
    }
    hideLoading();
  }
}
