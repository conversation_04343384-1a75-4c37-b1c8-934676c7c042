import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/base/cubit/base_cubit_state.dart';
import 'package:koc_app/src/modules/campaign/data/models/campaign.dart';

part 'campaign_search_state.freezed.dart';
part 'campaign_search_state.g.dart';

@freezed
class CampaignSearchState extends BaseCubitState with _$CampaignSearchState {
  factory CampaignSearchState({
    @Default([]) List<Category> categories,
    @Default([]) List<bool> isCategoryChecked,
    @Default(CampaignType.values) List<CampaignType> types,
    @Default([]) List<bool> isTypeChecked,
    @Default(false) bool isDataLoaded,
    @Default([]) List<DefaultCampaignSummary> campaignResults,
    @Default('') String searchText,
    @Default([]) List<String> searchTextHistories,
    @Default([]) List<DefaultCampaignSummary> textSearchResults,
    @Default('') String errorMessage,
    @Default(false) bool isLoading,
    @Default(false) bool isSearchingByKeyword,
    @Default('') String currency,
    @Default(false) bool hasSearched,
    @Default(1) int currentPage,
    @Default(10) int pageSize,
    @Default(false) bool hasMoreData,
    @Default(false) bool isLoadingMore,
  }) = _CampaignSearchState;

  factory CampaignSearchState.fromJson(Map<String, Object> json) =>
      _$CampaignSearchStateFromJson(json);
}
