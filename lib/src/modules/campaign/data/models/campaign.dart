// ignore_for_file: constant_identifier_names

import 'package:freezed_annotation/freezed_annotation.dart';


part 'campaign.freezed.dart';
part 'campaign.g.dart';

@freezed
class CampaignDetails with _$CampaignDetails {
  const factory CampaignDetails({
    required int id,
    required String name,
    required CampaignType type,
    required String url,
    String? imageUrl,
    DateTime? startDate,
    DateTime? endDate,
    @Default('') String description,
    @Default('') String englishDescription,
    @Default([]) List<CampaignBudget> budgets,
    @Default([]) List<DefaultReward> defaultRewards,
    @Default([]) List<ProductCategoryReward> categoryRewards,
    @Default([]) List<Category> categories,
    AffiliationStatus? affiliationStatus,
    String? currency,
    bool? isRewardsByCategoriesVisible,
    CampaignStatus? campaignStatus,
    List<String>? deviceTypes,
    List<String>? customerCountries,
    List<String>? rejectConditions,
    String? requiredActionLocal,
    String? requiredActionEnglish,
    String? rejectConditionLocal,
    String? rejectConditionEnglish,
    String? validationTermLocal,
    String? validationTermEnglish,
    String? conversionWindow,
    DateTime? appliedDate,
    DateTime? rejectedDate,
    bool? isReferralCampaign,
    List<String>? validTrafficRestrictions,
    List<String>? invalidTrafficRestrictions,
    CampaignApplication? campaignApplication,
    @Default(false) haveUpsizedReward
  }) = _CampaignDetails;

  factory CampaignDetails.fromJson(Map<String, dynamic> json) => _$CampaignDetailsFromJson(json);
}

@freezed
class ApplyCampaignRequest with _$ApplyCampaignRequest {
  factory ApplyCampaignRequest({
    required List<int> campaignIds,
    required int siteId,
  }) = _ApplyCampaignRequest;

  factory ApplyCampaignRequest.fromJson(Map<String, Object?> json) => _$ApplyCampaignRequestFromJson(json);
}

@freezed
class CampaignBudget with _$CampaignBudget {
  factory CampaignBudget({
    required CampaignBudgetType type,
    required int cap,
  }) = _CampaignBudget;

  factory CampaignBudget.fromJson(Map<String, Object> json) => _$CampaignBudgetFromJson(json);
}

@unfreezed
class DefaultReward with _$DefaultReward {
  factory DefaultReward({
    String? type,
    String? name,
    double? reward,
    double? previousReward,
    double? maxPublisherReward,
    double? rewardByPublisherCurrency,
    double? previousRewardByPublisherCurrency,
    String? customerType,
    String? customerTypeName,
    bool? isAllSameRewardAmount,
    String? targetTimeFrom,
    String? targetTimeTo,
    bool? isExpired,
  }) = _DefaultReward;

  factory DefaultReward.fromJson(Map<String, dynamic> json) => _$DefaultRewardFromJson(json);
}

@unfreezed
class ProductCategoryReward with _$ProductCategoryReward {
  factory ProductCategoryReward({
    DefaultReward? maxReward,
    @Default([]) List<DefaultReward> allCategoryRewards,
  }) = _ProductCategoryReward;

  factory ProductCategoryReward.fromJson(Map<String, Object> json) => _$ProductCategoryRewardFromJson(json);
}

@freezed
class UpsizedReward with _$UpsizedReward {
  factory UpsizedReward({
    RewardType? type,
    @Default('') String name,
    @Default(0) double previousReward,
    @Default(0) double reward,
    @Default('') String customerType,
    DateTime? targetTimeFrom,
    DateTime? targetTimeTo,
    @Default(false) bool isExpired,
  }) = _UpsizedReward;

  factory UpsizedReward.fromJson(Map<String, Object> json) => _$UpsizedRewardFromJson(json);
}

@freezed
class Category with _$Category {
  factory Category({
    required String name,
    required int value,
  }) = _Category;

  factory Category.fromJson(Map<String, dynamic> json) => _$CategoryFromJson(json);
}

@freezed
class DefaultCampaignSummary with _$DefaultCampaignSummary {
  const factory DefaultCampaignSummary({
    required final int id,
    String? name,
    String? imageUrl,
    String? category,
    @Default([]) List<HighestRewardSummary> highestRewardSummaries,
    @Default(false) bool haveUpsizedReward,
    @Default(false) bool isNew,
  }) = _DefaultCampaignSummary;

  factory DefaultCampaignSummary.fromJson(Map<String, dynamic> json) => _$DefaultCampaignSummaryFromJson(json);
}

@freezed
class HighestRewardSummary with _$HighestRewardSummary {
  const factory HighestRewardSummary({
    required String type,
    required int reward,
  }) = _HighestRewardSummary;

  factory HighestRewardSummary.fromJson(Map<String, dynamic> json) => _$HighestRewardSummaryFromJson(json);
}

@freezed
class CampaignSummary with _$CampaignSummary {
  factory CampaignSummary({
    @Default(0) int id,
    @Default("") String imageUrl,
    @Default("") String name,
    @Default("") String category,
  }) = _CampaignSummary;

  factory CampaignSummary.fromJson(Map<String, Object> json) => _$CampaignSummaryFromJson(json);
}

@freezed
class CampaignSearch with _$CampaignSearch {
  factory CampaignSearch({
    @Default([]) List<Category> categories,
    @Default([]) List<Category> selectedCategories,
    @Default(CampaignType.values) List<CampaignType> types,
    @Default([]) List<CampaignType> selectedTypes,
    @Default([]) List<DefaultCampaignSummary> campaignResults,
  }) = _CampaignSearch;

  factory CampaignSearch.fromJson(Map<String, Object> json) => _$CampaignSearchFromJson(json);
}

@freezed
class CampaignFilter with _$CampaignFilter {
  factory CampaignFilter({
    @Default('') String keyword,
    @Default(0) int excludedCampaignId,
    @Default([]) List<int> categoryIds,
    @Default([]) List<CampaignType> campaignTypes,
    @Default([]) List<CampaignApplication> campaignApplications,
    @Default([]) List<String> customerCountries,
    @Default(10) int limit,
    @Default(1) int page,
  }) = _CampaignFilter;

  factory CampaignFilter.fromJson(Map<String, dynamic> json) => _$CampaignFilterFromJson(json);
}

@freezed
class CampaignCountSummary with _$CampaignCountSummary {
  const factory CampaignCountSummary({
    @Default(0) int affiliatedCount,
    @Default(0) int availableCount,
    @Default(0) int pausedCount,
    @Default(0) int waitingCount,
  }) = _CampaignCountSummary;

  factory CampaignCountSummary.fromJson(Map<String, dynamic> json) => _$CampaignCountSummaryFromJson(json);
}

enum CampaignType {
  CPA('Cost Per Action (CPA)'),
  CPC('Cost Per Click (CPC)'),
  CPL('Cost Per Lead (CPL)'),
  CPS('Cost Per Sales (CPS)');

  final String value;

  const CampaignType(this.value);
}

enum SelfConversion {
  NOT_ALLOWED,
  ALLOWED,
  ALLOWED_ONCE;
}

enum CampaignBudgetType {
  CONVERSION_COUNT,
  CONVERSION_TOTAL_COMMISSION,
  CLICK_COUNT,
  CLICK_TOTAL_COMMISSION;
}

enum RewardType {
  CPC('Fixed'),
  CPA_FIXED('Fixed'),
  CPA_SALES('Sales');

  final String value;

  const RewardType(this.value);
}

enum AffiliationStatus {
  NEW,
  APPLYING,
  APPROVED,
  REJECTED,
  CANCELED;
}

enum CampaignViewType {
  PROMOTED,
  RECOMMENDED,
  NEW,
  WAITING,
  AFFILIATED,
  PAUSED;
}

enum TargetDeviceType {
  UNKNOWN('Unknown'),
  DESKTOP('PC'),
  FEATURE_PHONE('Feature Phone'),
  ANDROID('Android'),
  IPHONE('iPhone'),
  ANDROID_TAB('Android Tablet'),
  IPAD('iPad');

  final String value;

  const TargetDeviceType(this.value);
}

enum CampaignApplication {
  WEB_ONLY('Web Only'),
  MOBILE_APP_ONLY('Mobile App Only'),
  WEB_AND_MOBILE_APP('Web and Mobile App');

  final String value;

  const CampaignApplication(this.value);
}

enum CampaignStatus {
  GETTING_READY(0),
  RUNNING(1),
  PAUSED(3),
  TERMINATED(2),
  WONT_RUN(5),
  OTHER(4);

  final int value;

  const CampaignStatus(this.value);
}

enum CampaignTabIndex {
  AVAILABLE('Available'),
  WAITING('Waiting'),
  AFFILIATED('Affiliated'),
  PAUSED('Paused');

  final String label;

  const CampaignTabIndex(this.label);
}

enum CampaignDetailsTabIndex {
  DETAILS('Details'),
  BANNERS('Banners'),
  COUPONS('Coupons'),
  CUSTOM_LINKS('Custom Links');

  final String label;

  const CampaignDetailsTabIndex(this.label);
}
