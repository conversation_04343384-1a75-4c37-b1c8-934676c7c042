
import 'package:koc_app/src/modules/campaign/data/models/campaign.dart';
import 'package:koc_app/src/shared/services/api_service.dart';
import 'package:koc_app/src/shared/services/shared_preferences_service.dart';

class CampaignRepository {
  final ApiService apiService;
  final SharedPreferencesService sharedPreferencesService;

  CampaignRepository(this.apiService, this.sharedPreferencesService);

  Future<dynamic> findCampaignFeatureSummary(int siteId) async {
    return await apiService.getData('/v3/publishers/me/sites/$siteId/campaigns/featured-summary');
  }

  Future<dynamic> findTopCampaignSummary(int siteId) async {
    return await apiService.getData('/v3/publishers/me/sites/$siteId/campaigns/top-summary');
  }

  Future<dynamic> findFastestGrowingCampaignSummary(int siteId) async {
    return await apiService.getData('/v3/publishers/me/sites/$siteId/campaigns/fastest-growing-summary');
  }

  Future<CampaignCountSummary> findCampaignCountSummary(int siteId, {CampaignFilter? filter}) async {
    filter ??= CampaignFilter();
    final queries = _buildQueryParams(filter);
    final result = await apiService.getData('/v3/publishers/me/sites/$siteId/campaigns/count-summary', params: queries);
    return CampaignCountSummary.fromJson(result);
  }

  Future<dynamic> findAvailableCampaigns(int siteId, CampaignFilter filter) async {
    final queries = _buildQueryParams(filter);
    return await apiService.getData('/v3/publishers/me/sites/$siteId/campaigns/available', params: queries);
  }

  Future<dynamic> findAffiliatedCampaigns(int siteId, CampaignFilter filter) async {
    final queries = _buildQueryParams(filter);
    return await apiService.getData('/v3/publishers/me/sites/$siteId/campaigns/affiliated', params: queries);
  }

  Future<dynamic> findWaitingCampaigns(int siteId, CampaignFilter filter) async {
    final queries = _buildQueryParams(filter);
    return await apiService.getData('/v3/publishers/me/sites/$siteId/campaigns/waiting', params: queries);
  }

  Future<dynamic> findPausedCampaigns(int siteId, CampaignFilter filter) async {
    final queries = _buildQueryParams(filter);
    return await apiService.getData('/v3/publishers/me/sites/$siteId/campaigns/paused', params: queries);
  }

  Future<dynamic> findCustomCreativeEnabledCampaigns(int siteId) async {
    return await apiService.getData('/v3/publishers/me/sites/$siteId/affiliated/custom-creative-enabled-campaigns');
  }

  Future<dynamic> findPromotedCampaigns(int siteId) async {
    return await apiService
        .getData('/v3/publishers/me/campaigns/vouchers-available/names', params: {'siteId': siteId.toString()});
  }

  Future<dynamic> getDetailsBy(int siteId, int campaignId) async {
    final params = {
      'siteId': siteId.toString(),
    };
    return await apiService.getData('/v3/publisher/campaigns/$campaignId', params: params);
  }

  Future<List<UpsizedReward>> findUpsizedRewards(DateTime startDate, DateTime endDate) async {
    await Future.delayed(const Duration(seconds: 1));
    return [
      UpsizedReward(
        type: RewardType.CPC,
        name: 'Click Reward',
        previousReward: 10.0,
        reward: 15.0,
        customerType: 'New',
        targetTimeFrom: DateTime.now().subtract(const Duration(days: 5)),
        targetTimeTo: DateTime.now().subtract(const Duration(days: 3)),
        isExpired: false,
      ),
      UpsizedReward(
        type: RewardType.CPA_FIXED,
        name: 'Fixed Reward for Action',
        previousReward: 20.0,
        reward: 25.0,
        customerType: 'Existing',
        targetTimeFrom: DateTime.now().subtract(const Duration(days: 5)),
        targetTimeTo: DateTime.now().subtract(const Duration(days: 3)),
        isExpired: false,
      ),
      UpsizedReward(
        type: RewardType.CPA_SALES,
        name: 'Sales Reward',
        previousReward: 0.03,
        reward: 0.04,
        customerType: 'VIP',
        targetTimeFrom: DateTime.now().subtract(const Duration(days: 5)),
        targetTimeTo: DateTime.now().subtract(const Duration(days: 3)),
        isExpired: false,
      ),
      UpsizedReward(
        type: RewardType.CPC,
        name: 'Click Incentive',
        previousReward: 5.0,
        reward: 10.0,
        customerType: 'New',
        targetTimeFrom: DateTime.now().subtract(const Duration(days: 5)),
        targetTimeTo: DateTime.now().subtract(const Duration(days: 3)),
        isExpired: true,
      ),
      UpsizedReward(
        type: RewardType.CPA_FIXED,
        name: 'Limited Time Offer',
        previousReward: 15.0,
        reward: 20.0,
        customerType: 'Returning',
        targetTimeFrom: DateTime.now().subtract(const Duration(days: 5)),
        targetTimeTo: DateTime.now().subtract(const Duration(days: 3)),
        isExpired: false,
      )
    ];
  }

  Future<List<DefaultCampaignSummary>> getCampaigns(
      String userId, List<String> categories, List<CampaignType> types) async {
    final response = await apiService.getDataWithoutJwt('http://localhost:8080/v1/$userId/campaigns/home');
    if (response.statusCode == 200) {
      return response.data;
    } else {
      throw Exception('Failed to load campaigns');
    }
  }

  Future<int> applyCampaign(ApplyCampaignRequest request) async {
    return await apiService.postDataAndExtractStatusCode('/v3/publisher/campaigns/affiliate', request);
  }

  Future<List<dynamic>> findAffiliatedRejectedProhibitedCampaignNamesAndIdsForPublisher() async {
    Map<String, dynamic> params = {
      'siteId': (await sharedPreferencesService.getCurrentSiteId()).toString(),
    };
    return await apiService.getData('/v3/publishers/me/campaigns/affiliated-rejected-prohibited/names', params: params);
  }

  Map<String, dynamic> _buildQueryParams(CampaignFilter filter) {
    Map<String, dynamic> params = {
      if (filter.keyword != '') 'keyword': filter.keyword,
      'page': filter.page.toString(),
      'limit': filter.limit.toString(),
    };

    if (filter.campaignTypes.isNotEmpty) {
      params['campaignTypes'] = filter.campaignTypes.map((type) => type.name).toList();
    }

    if (filter.categoryIds.isNotEmpty) {
      params['categories'] = filter.categoryIds.map((id) => id.toString()).toList();
    }

    if (filter.campaignApplications.isNotEmpty) {
      params['campaignApplications'] = filter.campaignApplications.map((type) => type.name).toList();
    }

    return params;
  }

  Future<List<Category>> getCategories() async {
    try {
      final response = await apiService.getData('/v3/publishers/categories');
      if (response is List) {
        return response.map((item) => Category.fromJson(item as Map<String, dynamic>)).toList();
      }
      return [];
    } catch (e) {
      return [];
    }
  }

  Future<bool> isCustomLinksEnabled(int siteId, int campaignId) async {
    try {
      Map<String, dynamic> params = {
        'siteId': siteId.toString(),
      };
      return await apiService.getData('/v3/campaigns/$campaignId/creatives/availability', params: params);
    } catch (e) {
      return false;
    }
  }
}
