import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/notification/cubit/notification_details_cubit.dart';
import 'package:koc_app/src/modules/notification/cubit/notification_details_state.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/widgets/cached_image_with_placeholder.dart';

class NotificationDetailsPage extends StatefulWidget {
  final int id;
  const NotificationDetailsPage({required this.id, super.key});

  @override
  State<NotificationDetailsPage> createState() => _NotificationDetailsPageState();
}

class _NotificationDetailsPageState extends BasePageState<NotificationDetailsPage, NotificationDetailsCubit> {
  @override
  void initState() {
    doLoadingAction(() async {
      await cubit.findNotificationBy(widget.id);
    });
    super.initState();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      body: BlocBuilder<NotificationDetailsCubit, NotificationDetailsState>(builder: (_, state) {
        if (state.notificationDetails != null) {
          return Column(
            children: [
              _buildAppBar(state),
              _buildBody(state),
            ],
          );
        }
        return const SizedBox.shrink();
      }),
    );
  }

  Widget _buildAppBar(NotificationDetailsState state) {
    return Padding(
      padding: EdgeInsets.all(16.r),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Text(
              state.notificationDetails!.subject,
              style: context.textTitleSmall(fontWeight: FontWeight.w500),
            ),
          ),
          GestureDetector(
            onTap: () {
              Navigator.of(context).pop();
            },
            child: CircleAvatar(
              backgroundColor: Colors.grey[200],
              radius: 12.r,
              child: Icon(Icons.close, size: 16.r),
            ),
          )
        ],
      ),
    );
  }

  Widget _buildBody(NotificationDetailsState state) {
    return Expanded(
      child: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: Column(
            spacing: 16.r,
            children: [
              CachedImageWithPlaceholder(
                width: double.infinity,
                imageUrl: state.notificationDetails!.imageUrl,
              ),
              HtmlWidget(
                state.notificationDetails!.description,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
