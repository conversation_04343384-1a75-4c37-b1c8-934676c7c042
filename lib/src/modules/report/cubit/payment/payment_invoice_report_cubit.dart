import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/report/cubit/payment/payment_invoice_report_state.dart';
import 'package:koc_app/src/modules/report/data/model/payment/invoice_detail_response.dart';
import 'package:koc_app/src/modules/report/data/repository/report_repository.dart';
import 'package:koc_app/src/shared/services/api_service.dart';
import 'package:koc_app/src/shared/utils/handle_error.dart';

class PaymentInvoiceReportCubit extends BaseCubit<PaymentInvoiceReportState> {
  final ReportRepository reportRepository;
  PaymentInvoiceReportCubit(this.reportRepository) : super(PaymentInvoiceReportState());

  Future<void> findInvoiceReport(String invoiceId) async {
    try {
      final results = await Future.wait([
        reportRepository.findInvoiceDetail(invoiceId),
        commonCubit.sharedPreferencesService.getPublisherCurrency(),
      ]);
      emit(state.copyWith(
        invoiceDetailResponse: results[0] as InvoiceDetailResponse,
        currency: results[1] as String,
      ));
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  /// Comprehensive refresh method for pull-to-refresh functionality
  /// This method refreshes invoice data while bypassing cache to ensure fresh data
  /// Uses isPullToRefresh flag to prevent multiple loading indicators
  Future<void> pullToRefresh(String invoiceId) async {
    try {
      emit(state.copyWith(isPullToRefresh: true));

      final apiService = Modular.get<ApiService>();

      await apiService.clearCacheForEndpoint('/v3/publishers/me/payment/invoice-detail/$invoiceId');

      await _refreshInvoiceDataForPullToRefresh(invoiceId);
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    } finally {
      emit(state.copyWith(isPullToRefresh: false));
    }
  }

  /// Specialized refresh method for invoice data during pull-to-refresh
  /// This method doesn't trigger loading states to avoid multiple loading indicators
  Future<void> _refreshInvoiceDataForPullToRefresh(String invoiceId) async {
    try {
      final results = await Future.wait([
        reportRepository.findInvoiceDetail(invoiceId),
        commonCubit.sharedPreferencesService.getPublisherCurrency(),
      ]);

      emit(state.copyWith(
        invoiceDetailResponse: results[0] as InvoiceDetailResponse,
        currency: results[1] as String,
      ));
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }
}
