import 'package:dio/dio.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/report/cubit/campaign/campaign_detail_report_state.dart';
import 'package:koc_app/src/modules/report/data/model/report_model.dart';
import 'package:koc_app/src/modules/report/data/repository/report_repository.dart';
import 'package:koc_app/src/shared/data/item.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/services/api_service.dart';
import 'package:koc_app/src/shared/utils/handle_error.dart';

class CampaignDetailReportCubit extends BaseCubit<CampaignDetailReportState> {
  final ReportRepository reportRepository;
  CampaignDetailReportCubit(this.reportRepository) : super(const CampaignDetailReportState());

  Future<void> findConversions(
      int campaignId, DateTime startDate, DateTime endDate, ReportQueryPeriodBase dateType, Item? site,
      {int page = 1, int pageSize = 10}) async {
    try {
      String? countryCode = await commonCubit.sharedPreferencesService.getCountryCode();
      String? currency = await commonCubit.sharedPreferencesService.getPublisherCurrency();
      int? siteId = await commonCubit.sharedPreferencesService.getCurrentSiteId();

      final response = await reportRepository.findCampaignDetailReportData(
          campaignId,
          startDate.toZonedIso8601(countryCode!.toCountry),
          endDate.toZonedIso8601(countryCode.toCountry),
          dateType,
          site?.value ?? siteId,
          page,
          pageSize);

      emit(state.copyWith(
          currency: currency!,
          reportData: response.content,
          currentPage: page,
          pageSize: pageSize,
          totalCount: response.totalItems));
    } on DioException catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  /// Comprehensive refresh method for pull-to-refresh functionality
  /// This method refreshes campaign detail data while bypassing cache to ensure fresh data
  Future<void> pullToRefresh(
      int campaignId, DateTime startDate, DateTime endDate, ReportQueryPeriodBase dateType, Item? site,
      {int page = 1, int pageSize = 10}) async {
    try {
      final apiService = Modular.get<ApiService>();

      await apiService.clearCacheForEndpoint('/v3/publishers/me/reports/conversion/detail');

      await findConversions(campaignId, startDate, endDate, dateType, site, page: page, pageSize: pageSize);
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }
}
