// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'campaign_detail_report_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CampaignDetailReportStateImpl _$$CampaignDetailReportStateImplFromJson(
        Map<String, dynamic> json) =>
    _$CampaignDetailReportStateImpl(
      currency: json['currency'] as String? ?? '',
      reportData: (json['reportData'] as List<dynamic>?)
              ?.map((e) =>
                  CampaignDetailReportData.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      errorMessage: json['errorMessage'] as String? ?? '',
      totalCount: (json['totalCount'] as num?)?.toInt() ?? 0,
      currentPage: (json['currentPage'] as num?)?.toInt() ?? 1,
      pageSize: (json['pageSize'] as num?)?.toInt() ?? 10,
    );

Map<String, dynamic> _$$CampaignDetailReportStateImplToJson(
        _$CampaignDetailReportStateImpl instance) =>
    <String, dynamic>{
      'currency': instance.currency,
      'reportData': instance.reportData,
      'errorMessage': instance.errorMessage,
      'totalCount': instance.totalCount,
      'currentPage': instance.currentPage,
      'pageSize': instance.pageSize,
    };
