import 'package:dio/dio.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/report/cubit/campaign/campaign_report_state.dart';
import 'package:koc_app/src/modules/report/data/model/report_model.dart';
import 'package:koc_app/src/modules/report/data/repository/report_repository.dart';
import 'package:koc_app/src/shared/data/item.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/services/api_service.dart';
import 'package:koc_app/src/shared/utils/handle_error.dart';

class CampaignReportCubit extends BaseCubit<CampaignReportState> {
  final ReportRepository reportRepository;
  CampaignReportCubit(this.reportRepository) : super(CampaignReportState());

  void hideReport() {
    emit(state.copyWith(showReport: false));
  }

  Future<void> findCampaigns(DateTime fromDate, DateTime toDate, ReportQueryPeriodBase periodBase,
      ConversionStatus? status, Item? site) async {
    try {
      String? countryCode = await commonCubit.sharedPreferencesService.getCountryCode();
      int? siteId = await commonCubit.sharedPreferencesService.getCurrentSiteId();
      List<dynamic> results = await Future.wait([
        commonCubit.sharedPreferencesService.getPublisherCurrency(),
        reportRepository.findCampaignReportData(
          fromDate.toZonedIso8601(countryCode!.toCountry),
          toDate.toZonedIso8601(countryCode.toCountry),
          periodBase,
          status,
          site?.value ?? siteId,
        ),
      ]);
      emit(state.copyWith(currency: results[0], reportData: results[1], showReport: true));
    } on DioException catch (e) {
      handleError(e, (message) => {emit(state.copyWith(errorMessage: message))});
    }
  }

  void updateChartTitle(CampaignChartTitle title) {
    emit(state.copyWith(chartTitle: title));
  }

  void updateChartValue(CampaignChartValue value) {
    emit(state.copyWith(chartValue: value));
  }

  /// Comprehensive refresh method for pull-to-refresh functionality
  /// This method refreshes campaign data while bypassing cache to ensure fresh data
  Future<void> pullToRefresh(DateTime fromDate, DateTime toDate, ReportQueryPeriodBase periodBase,
      ConversionStatus? status, Item? site) async {
    try {
      final apiService = Modular.get<ApiService>();

      await apiService.clearCacheForEndpoint('/v3/publishers/me/reports/campaign');

      await findCampaigns(fromDate, toDate, periodBase, status, site);
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }
}
