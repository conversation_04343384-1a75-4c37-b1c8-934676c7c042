// ignore_for_file: constant_identifier_names

enum ReportPeriod {
  TODAY('Today'),
  YESTERDAY('Yesterday'),
  LAST_7_DAYS('Last 7 days'),
  LAST_14_DAYS('Last 14 days'),
  LAST_30_DAYS('Last 30 days'),
  THIS_WEEK('This week'),
  LAST_WEEK('Last week'),
  THIS_MONTH('This month'),
  LAST_MONTH('Last month'),
  LAST_3_MONTHS('Last 3 months'),
  LAST_6_MONTHS('Last 6 months'),
  LAST_12_MONTHS('Last 12 months'),
  NEXT_3_MONTHS('Next 3 months'),
  NEXT_6_MONTHS('Next 6 months'),
  THIS_YEAR('This year'),
  LAST_YEAR('Last year'),
  CUSTOM_RANGE('Custom range');

  final String value;
  const ReportPeriod(this.value);
}

enum ReportQueryPeriodBase {
  CONVERSION_DATE('Conversion date'),
  CONFIRMATION_DATE('Confirmation date'),
  POSTBACK_ERROR_DATE('Postback error date'),
  UPDATE_DATE('Update date'),
  PAID_DATE('Paid date');

  final String value;
  const ReportQueryPeriodBase(this.value);
}

enum ConversionStatus {
  PENDING,
  APPROVED,
  REJECTED;
}

enum PerformanceChartTitle {
  CLICKS('Clicks'),
  CONVERSIONS('Conversions'),
  REWARD('Reward'),
  CVR('CVR'),
  EPC('EPC');

  final String value;
  const PerformanceChartTitle(this.value);
}