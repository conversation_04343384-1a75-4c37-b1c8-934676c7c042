// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'async_export_conversion_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AsyncExportConversionRequestImpl _$$AsyncExportConversionRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$AsyncExportConversionRequestImpl(
      fromDate: json['fromDate'] == null
          ? null
          : DateTime.parse(json['fromDate'] as String),
      toDate: json['toDate'] == null
          ? null
          : DateTime.parse(json['toDate'] as String),
      periodBase: $enumDecodeNullable(
              _$ReportQueryPeriodBaseEnumMap, json['periodBase']) ??
          ReportQueryPeriodBase.CONVERSION_DATE,
      conversionStatuses: (json['conversionStatuses'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$ConversionStatusEnumMap, e))
          .toList(),
      campaignIds: (json['campaignIds'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      siteIds: (json['siteIds'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      invoiceNumber: json['invoiceNumber'] as String?,
      locale: json['locale'] as String?,
    );

Map<String, dynamic> _$$AsyncExportConversionRequestImplToJson(
        _$AsyncExportConversionRequestImpl instance) =>
    <String, dynamic>{
      'fromDate': instance.fromDate?.toIso8601String(),
      'toDate': instance.toDate?.toIso8601String(),
      'periodBase': _$ReportQueryPeriodBaseEnumMap[instance.periodBase]!,
      'conversionStatuses': instance.conversionStatuses
          ?.map((e) => _$ConversionStatusEnumMap[e]!)
          .toList(),
      'campaignIds': instance.campaignIds,
      'siteIds': instance.siteIds,
      'invoiceNumber': instance.invoiceNumber,
      'locale': instance.locale,
    };

const _$ReportQueryPeriodBaseEnumMap = {
  ReportQueryPeriodBase.CONVERSION_DATE: 'CONVERSION_DATE',
  ReportQueryPeriodBase.CONFIRMATION_DATE: 'CONFIRMATION_DATE',
  ReportQueryPeriodBase.POSTBACK_ERROR_DATE: 'POSTBACK_ERROR_DATE',
  ReportQueryPeriodBase.UPDATE_DATE: 'UPDATE_DATE',
  ReportQueryPeriodBase.PAID_DATE: 'PAID_DATE',
};

const _$ConversionStatusEnumMap = {
  ConversionStatus.PENDING: 'PENDING',
  ConversionStatus.APPROVED: 'APPROVED',
  ConversionStatus.REJECTED: 'REJECTED',
};
