// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'performance_report_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PerformanceReportDataImpl _$$PerformanceReportDataImplFromJson(
        Map<String, dynamic> json) =>
    _$PerformanceReportDataImpl(
      date:
          json['date'] == null ? null : DateTime.parse(json['date'] as String),
      clicks: (json['clicks'] as num?)?.toInt() ?? 0,
      conversions: (json['conversions'] as num?)?.toInt() ?? 0,
      reward: (json['reward'] as num?)?.toDouble() ?? 0,
      epc: (json['epc'] as num?)?.toDouble() ?? 0,
    );

Map<String, dynamic> _$$PerformanceReportDataImplToJson(
        _$PerformanceReportDataImpl instance) =>
    <String, dynamic>{
      'date': instance.date?.toIso8601String(),
      'clicks': instance.clicks,
      'conversions': instance.conversions,
      'reward': instance.reward,
      'epc': instance.epc,
    };

_$PerformanceChartDataImpl _$$PerformanceChartDataImplFromJson(
        Map<String, dynamic> json) =>
    _$PerformanceChartDataImpl(
      date: json['date'] as String,
      clicks: (json['clicks'] as num).toInt(),
      conversions: (json['conversions'] as num).toInt(),
    );

Map<String, dynamic> _$$PerformanceChartDataImplToJson(
        _$PerformanceChartDataImpl instance) =>
    <String, dynamic>{
      'date': instance.date,
      'clicks': instance.clicks,
      'conversions': instance.conversions,
    };

_$FindReportSummaryRequestImpl _$$FindReportSummaryRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$FindReportSummaryRequestImpl(
      fromDate: DateTime.parse(json['fromDate'] as String),
      toDate: DateTime.parse(json['toDate'] as String),
      compareFromDate: DateTime.parse(json['compareFromDate'] as String),
      compareToDate: DateTime.parse(json['compareToDate'] as String),
    );

Map<String, dynamic> _$$FindReportSummaryRequestImplToJson(
        _$FindReportSummaryRequestImpl instance) =>
    <String, dynamic>{
      'fromDate': instance.fromDate.toIso8601String(),
      'toDate': instance.toDate.toIso8601String(),
      'compareFromDate': instance.compareFromDate.toIso8601String(),
      'compareToDate': instance.compareToDate.toIso8601String(),
    };

_$FindPerformanceChartRequestImpl _$$FindPerformanceChartRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$FindPerformanceChartRequestImpl(
      siteId: (json['siteId'] as num).toInt(),
      fromDate: DateTime.parse(json['fromDate'] as String),
      toDate: DateTime.parse(json['toDate'] as String),
      periodBase:
          $enumDecode(_$ReportQueryPeriodBaseEnumMap, json['periodBase']),
    );

Map<String, dynamic> _$$FindPerformanceChartRequestImplToJson(
        _$FindPerformanceChartRequestImpl instance) =>
    <String, dynamic>{
      'siteId': instance.siteId,
      'fromDate': instance.fromDate.toIso8601String(),
      'toDate': instance.toDate.toIso8601String(),
      'periodBase': _$ReportQueryPeriodBaseEnumMap[instance.periodBase]!,
    };

const _$ReportQueryPeriodBaseEnumMap = {
  ReportQueryPeriodBase.CONVERSION_DATE: 'CONVERSION_DATE',
  ReportQueryPeriodBase.CONFIRMATION_DATE: 'CONFIRMATION_DATE',
  ReportQueryPeriodBase.POSTBACK_ERROR_DATE: 'POSTBACK_ERROR_DATE',
  ReportQueryPeriodBase.UPDATE_DATE: 'UPDATE_DATE',
  ReportQueryPeriodBase.PAID_DATE: 'PAID_DATE',
};

_$ReportSummaryImpl _$$ReportSummaryImplFromJson(Map<String, dynamic> json) =>
    _$ReportSummaryImpl(
      earnings: json['earnings'] == null
          ? const Metric()
          : Metric.fromJson(json['earnings'] as Map<String, dynamic>),
      clicks: json['clicks'] == null
          ? const Metric()
          : Metric.fromJson(json['clicks'] as Map<String, dynamic>),
      conversions: json['conversions'] == null
          ? const Metric()
          : Metric.fromJson(json['conversions'] as Map<String, dynamic>),
      earningsPerClick: json['earningsPerClick'] == null
          ? const Metric()
          : Metric.fromJson(json['earningsPerClick'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$ReportSummaryImplToJson(_$ReportSummaryImpl instance) =>
    <String, dynamic>{
      'earnings': instance.earnings,
      'clicks': instance.clicks,
      'conversions': instance.conversions,
      'earningsPerClick': instance.earningsPerClick,
    };

_$MetricImpl _$$MetricImplFromJson(Map<String, dynamic> json) => _$MetricImpl(
      current: (json['current'] as num?)?.toDouble() ?? 0,
      changedPercentage: (json['changedPercentage'] as num?)?.toDouble() ?? 0,
      changedValue: (json['changedValue'] as num?)?.toDouble() ?? 0,
    );

Map<String, dynamic> _$$MetricImplToJson(_$MetricImpl instance) =>
    <String, dynamic>{
      'current': instance.current,
      'changedPercentage': instance.changedPercentage,
      'changedValue': instance.changedValue,
    };
