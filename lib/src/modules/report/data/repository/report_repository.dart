import 'package:koc_app/src/modules/report/data/model/campaign/campaign_detail_report_response.dart';
import 'package:koc_app/src/modules/report/data/model/campaign/campaign_report_data.dart';
import 'package:koc_app/src/modules/report/data/model/campaign/campaign_top_ten_click_response.dart';
import 'package:koc_app/src/modules/report/data/model/campaign_transaction_report_data.dart';
import 'package:koc_app/src/modules/report/data/model/conversion/conversion_campaign_summary_response.dart';
import 'package:koc_app/src/modules/report/data/model/conversion/conversion_summary_response.dart';
import 'package:koc_app/src/modules/report/data/model/payment/invoice_detail_response.dart';
import 'package:koc_app/src/modules/report/data/model/payment/minimum_payment_details.dart';
import 'package:koc_app/src/modules/report/data/model/payment/payment_process_summary.dart';
import 'package:koc_app/src/modules/report/data/model/payment/payment_report_data.dart';
import 'package:koc_app/src/modules/report/data/model/payment/payment_summary.dart';
import 'package:koc_app/src/modules/report/data/model/performance_daily_report_data.dart';
import 'package:koc_app/src/modules/report/data/model/performance_monthly_report_data.dart';
import 'package:koc_app/src/modules/report/data/model/performance_report_data.dart';
import 'package:koc_app/src/modules/report/data/model/report_model.dart';
import 'package:koc_app/src/shared/data/item.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/services/api_service.dart';
import 'package:koc_app/src/shared/services/shared_preferences_service.dart';

class ReportRepository {
  final ApiService apiService;
  final SharedPreferencesService sharedPreferencesService;

  ReportRepository(this.apiService, this.sharedPreferencesService);

  Future<dynamic> requestDownloadCsv(String fromDate, String toDate, ReportQueryPeriodBase periodBase,
      ConversionStatus? status, Item? campaign, int siteId) async {
    Map<String, dynamic> body = {
      'fromDate': fromDate,
      'toDate': toDate,
      'periodBase': periodBase.name,
      'siteIds': [siteId.toString()],
    };
    if (campaign != null) {
      body['campaignIds'] = [campaign.value.toString()];
    }
    if (status != null) {
      body['conversionStatuses'] = [status.name];
    }
    return apiService.putData('/v3/publishers/me/reports/conversion/async-export', body);
  }

  Future<List<CampaignTransactionReportData>> findConversionCampaignTransactionReportData(int campaignId,
      String transactionId, String conversionTime, String? confirmationTime, ConversionStatus status) async {
    Map<String, dynamic> params = {
      'campaignId': campaignId.toString(),
      'transactionId': transactionId,
      'conversionTime': conversionTime,
      'status': status.name,
    };
    if (confirmationTime != null) {
      params['confirmationTime'] = confirmationTime;
    }
    final result = await apiService.getData('/v3/publishers/me/reports/conversion/product-detail', params: {
      'campaignId': campaignId.toString(),
      'transactionId': transactionId,
      'conversionTime': conversionTime,
      'status': status.name,
    });
    List<CampaignTransactionReportData> reportData = List<CampaignTransactionReportData>.from(
      result.map((e) => CampaignTransactionReportData.fromJson(e as Map<String, dynamic>)),
    );
    return reportData;
  }

  Future<dynamic> findPerformanceChart(FindPerformanceChartRequest request) async {
    final countryCode = await sharedPreferencesService.getCountryCode() ?? '';
    final queryParams = {
      'siteId': request.siteId.toString(),
      'fromDate': request.fromDate.toZonedIso8601(countryCode.toCountry),
      'toDate': request.toDate.toZonedIso8601(countryCode.toCountry),
      'periodBase': request.periodBase.name.toString(),
    };
    return await apiService.getData('/v3/publishers/me/performance/chart', params: queryParams);
  }

  Future<PaymentSummary> findPaymentSummary() async {
    return PaymentSummary.fromJson(await apiService.getData('/v3/publishers/me/payment-summary'));
  }

  Future<MinimumPaymentDetails> findMinimumPaymentDetails() async {
    return MinimumPaymentDetails.fromJson(await apiService.getData('/v3/publishers/countries/minimum-payment-details'));
  }

  Future<dynamic> findReportSummary(FindReportSummaryRequest request) async {
    final countryCode = await sharedPreferencesService.getCountryCode() ?? '';
    final queryParams = {
      'fromDate': request.fromDate.toZonedIso8601(countryCode.toCountry),
      'toDate': request.toDate.toZonedIso8601(countryCode.toCountry),
      'compareFromDate': request.compareFromDate.toZonedIso8601(countryCode.toCountry),
      'compareToDate': request.compareToDate.toZonedIso8601(countryCode.toCountry),
    };
    return await apiService.getData('/v3/publishers/me/reports/summary', params: queryParams);
  }

  Future<List<PaymentReportData>> findPayments(String fromMonth, String toMonth, String? invoiceNumber) async {
    Map<String, dynamic> params = {
      'fromMonth': fromMonth,
      'toMonth': toMonth,
    };
    if (invoiceNumber != null && invoiceNumber.isNotEmpty) {
      params['invoiceNumber'] = invoiceNumber;
    }

    final result = await apiService.getData('/v3/publishers/me/payment', params: params);
    List<PaymentReportData> reportData =
        List<PaymentReportData>.from(result.map((e) => PaymentReportData.fromJson(e as Map<String, dynamic>)));
    return reportData;
  }

  Future<List<PerformanceMonthlyReportData>> findPerformanceMonthlyReportData(
    String fromMonth,
    String toMonth,
    ReportQueryPeriodBase periodBase,
    ConversionStatus? conversionStatus,
    int? campaignId,
    int siteId,
  ) async {
    Map<String, dynamic> params = {
      'fromMonth': fromMonth,
      'toMonth': toMonth,
      'periodBase': periodBase.name,
      'siteId': siteId.toString(),
    };
    if (campaignId != null) {
      params['campaignId'] = campaignId.toString();
    }
    if (conversionStatus != null) {
      params['status'] = conversionStatus.name;
    }

    List<dynamic> result = await apiService.getData('/v3/publishers/me/reports/monthly', params: params);
    return result.map((e) => PerformanceMonthlyReportData.fromJson(e)).toList();
  }

  Future<List<PerformanceDailyReportData>> findPerformanceDailyReportData(String fromDate, String toDate,
      ReportQueryPeriodBase periodBase, ConversionStatus? conversionStatus, int? campaignId, int siteId) async {
    Map<String, dynamic> params = {
      'fromDate': fromDate,
      'toDate': toDate,
      'periodBase': periodBase.name,
      'siteId': siteId.toString(),
    };
    if (campaignId != null) {
      params['campaignId'] = campaignId.toString();
    }
    if (conversionStatus != null) {
      params['status'] = conversionStatus.name;
    }

    List<dynamic> result = await apiService.getData('/v3/publishers/me/reports/daily', params: params);
    return result.map((e) => PerformanceDailyReportData.fromJson(e)).toList();
  }

  Future<ConversionSummaryResponse> findConversionSummary(int siteId) async {
    final result =
        await apiService.getData('/v3/publishers/me/reports/conversion-summary', params: {'siteId': siteId.toString()});
    return ConversionSummaryResponse.fromJson(result);
  }

  Future<ConversionCampaignSummaryResponse> findConversionCampaignSummary(
    String fromDate,
    String toDate,
    ReportQueryPeriodBase periodBase,
    int page,
    int limit,
    List<int>? campaignIds,
    int siteId,
  ) async {
    Map<String, dynamic> params = {
      'fromDate': fromDate,
      'toDate': toDate,
      'periodBase': periodBase.name,
      'siteId': siteId.toString(),
      'page': page.toString(),
      'limit': limit.toString(),
    };
    if (campaignIds != null) {
      params['campaignIds'] = campaignIds.map((id) => id.toString()).toList();
    }

    final result = await apiService.getData('/v3/publishers/me/reports/conversion/campaign-summary', params: params);
    return ConversionCampaignSummaryResponse.fromJson(result);
  }

  Future<CampaignDetailReportResponse> findCampaignDetailReportData(
    int campaignId,
    String fromDate,
    String toDate,
    ReportQueryPeriodBase periodBase,
    int siteId,
    int page,
    int limit,
  ) async {
    Map<String, dynamic> params = {
      'fromDate': fromDate,
      'toDate': toDate,
      'periodBase': periodBase.name,
      'siteId': siteId.toString(),
      'campaignId': campaignId.toString(),
      'page': page.toString(),
      'limit': limit.toString(),
    };

    final result = await apiService.getData('/v3/publishers/me/reports/conversion/detail', params: params);
    CampaignDetailReportResponse campaignDetailReportResponse =
        CampaignDetailReportResponse.fromJson(result as Map<String, dynamic>);

    return campaignDetailReportResponse;
  }

  Future<List<CampaignReportData>> findCampaignReportData(
    String fromDate,
    String toDate,
    ReportQueryPeriodBase periodBase,
    ConversionStatus? status,
    int siteId,
  ) async {
    Map<String, dynamic> params = {
      'fromDate': fromDate,
      'toDate': toDate,
      'periodBase': periodBase.name,
      'siteId': siteId.toString(),
    };
    if (status != null) {
      params['conversionStatus'] = status.name;
    }
    final result = await apiService.getData('/v3/publishers/me/reports/campaign', params: params);
    List<CampaignReportData> reportData = List<CampaignReportData>.from(
      result.map((e) => CampaignReportData.fromJson(e as Map<String, dynamic>)),
    );
    return reportData;
  }

  Future<CampaignTopTenClickResponse> findTopTenCampaignsClickCount(String fromDate, String toDate, int siteId) async {
    Map<String, dynamic> params = {
      'fromDate': fromDate,
      'toDate': toDate,
      'periodBase': ReportQueryPeriodBase.CONVERSION_DATE.name,
      'siteId': siteId.toString(),
      'limit': '10',
      'sortBy': 'CLICKS'
    };
    return CampaignTopTenClickResponse.fromJson(await apiService.getData(
      '/v3/publishers/me/reports/campaign/chart',
      params: params,
    ));
  }

  Future<InvoiceDetailResponse> findInvoiceDetail(String invoiceId) async {
    final result = await apiService.getData('/v3/publishers/me/payment/invoice-detail/$invoiceId');
    return InvoiceDetailResponse.fromJson(result);
  }

  Future<dynamic> findPaymentProcessStageDetails(PaymentProcessType type) async {
    return await apiService.getData('/v3/publishers/me/payment/process-stage-details', params: {
      'type': type.name,
    });
  }
}
