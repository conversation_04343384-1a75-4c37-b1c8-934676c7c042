import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/report/cubit/conversion/conversion_report_cubit.dart';
import 'package:koc_app/src/modules/report/cubit/conversion/conversion_report_state.dart';
import 'package:koc_app/src/modules/report/data/model/report_model.dart';
import 'package:koc_app/src/modules/report/mixin/report_mixin.dart';
import 'package:koc_app/src/modules/shared/cubit/filter_cubit.dart';
import 'package:koc_app/src/modules/shared/cubit/filter_state.dart';
import 'package:koc_app/src/modules/shared/mixin/filter_mixin.dart';
import 'package:koc_app/src/modules/report/presentation/conversion/conversion_filter_page.dart';
import 'package:koc_app/src/shared/constants/color_constants.dart';
import 'package:koc_app/src/shared/data/common_data_table_source.dart';
import 'package:koc_app/src/shared/data/item.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/mixin/common_mixin.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';
import 'package:koc_app/src/shared/widgets/pull_to_refresh_wrapper.dart';

class ConversionReportPage extends StatefulWidget {
  const ConversionReportPage({super.key});

  @override
  State<ConversionReportPage> createState() => _ConversionReportPageState();
}

class _ConversionReportPageState extends BasePageState<ConversionReportPage, ConversionReportCubit>
    with ReportMixin, CommonMixin, FilterMixin {
  late ScaffoldMessengerState _scaffoldMessengerState;
  late String _email;

  @override
  void initState() {
    _initializeFilterWithDefaultPeriod();
    commonCubit.sharedPreferencesService.getEmail().then((email) {
      if (email != null) {
        _email = email;
      }
    });
    super.initState();
  }

  /// Initialize filter with default period set to Last 7 days for conversion report
  void _initializeFilterWithDefaultPeriod() {
    Modular.get<FilterCubit>().clearWithDefaultPeriod(ReportPeriod.LAST_7_DAYS);
  }

  /// Custom showFilters method for conversion report with Last 7 days as default after clear
  Future<bool?> _showConversionFilters(BuildContext context) async {
    return await showModalBottomSheet<bool>(
      context: context,
      useSafeArea: true,
      isScrollControlled: true,
      builder: (_) {
        return ClipRRect(
          borderRadius: BorderRadius.circular(12.r),
          child: const ConversionFilterPage(
            [
              ReportPeriod.TODAY,
              ReportPeriod.YESTERDAY,
              ReportPeriod.LAST_7_DAYS,
              ReportPeriod.LAST_14_DAYS,
              ReportPeriod.LAST_30_DAYS,
              ReportPeriod.THIS_WEEK,
              ReportPeriod.LAST_WEEK,
              ReportPeriod.THIS_MONTH,
              ReportPeriod.LAST_MONTH,
              ReportPeriod.CUSTOM_RANGE
            ],
            showSites: true,
          ),
        );
      },
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _scaffoldMessengerState = ScaffoldMessenger.of(context);
  }

  @override
  void dispose() {
    _scaffoldMessengerState.clearSnackBars();
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: const Text('Conversion'),
        customAction: Row(
          children: [
            BlocBuilder<ConversionReportCubit, ConversionReportState>(builder: (_, state) {
              if (state.reportData != null && state.reportData!.campaignSummaries.isNotEmpty) {
                return IconButton(
                  icon: Icon(
                    Icons.file_download_outlined,
                    size: 20.r,
                  ),
                  onPressed: () {
                    showConfirmationDialog(
                      context,
                      Text(
                        'Download all via email?',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      RichText(
                        text: TextSpan(
                          style: Theme.of(context).textTheme.labelLarge,
                          children: [
                            const TextSpan(text: 'A download link will be sent to your email address '),
                            TextSpan(
                              text: _email,
                              style: Theme.of(context).textTheme.labelLarge!.copyWith(fontWeight: FontWeight.bold),
                            ),
                            const TextSpan(text: ' when the report is ready.')
                          ],
                        ),
                      ),
                      'Ok',
                      () {
                        doLoadingAction(() async {
                          FilterState filterState = Modular.get<FilterCubit>().state;
                          bool result = await cubit.requestCsvDownload(
                              filterState.startDate!,
                              filterState.endDate!,
                              filterState.selectedDateType,
                              filterState.selectedStatus,
                              filterState.selectedCampaign,
                              filterState.selectedSite);
                          if (context.mounted) {
                            if (result) {
                              context.showSnackBar(
                                'It may take several minutes for the system to prepare the report. Please kindly wait for the email. Thank you.',
                                title: 'Your request has been accepted!',
                                durationSecond: 0,
                              );
                            } else {
                              context.showSnackBar(
                                'Failed to send email. Please try again later.',
                                title: 'Error',
                                durationSecond: 0,
                              );
                            }
                          }
                        });
                        Modular.to.pop();
                      },
                      true,
                    );
                  },
                );
              }
              return const SizedBox.shrink();
            }),
            IconButton(
                onPressed: () async {
                  bool? showReport = await _showConversionFilters(context);
                  if (showReport != null && showReport) {
                    _findConversions(1, 10);
                  }
                },
                icon: const Icon(Icons.tune)),
          ],
        ),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return BlocBuilder<ConversionReportCubit, ConversionReportState>(
      bloc: cubit,
      builder: (_, state) {
        if (state.showReport) {
          return PullToRefreshWrapper(
            onRefresh: _refreshData,
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: Column(
                children: [
                  BlocBuilder<FilterCubit, FilterState>(
                    bloc: Modular.get<FilterCubit>(),
                    builder: (_, reportFilterState) {
                      if (state.showReport && reportFilterState != FilterState()) {
                        return buildFilterRow(context, reportFilterState, showSites: true, periodTemplate: [
                          ReportPeriod.TODAY,
                          ReportPeriod.YESTERDAY,
                          ReportPeriod.LAST_7_DAYS,
                          ReportPeriod.LAST_14_DAYS,
                          ReportPeriod.LAST_30_DAYS,
                          ReportPeriod.THIS_WEEK,
                          ReportPeriod.LAST_WEEK,
                          ReportPeriod.THIS_MONTH,
                          ReportPeriod.LAST_MONTH,
                        ]);
                      }
                      return const SizedBox.shrink();
                    },
                  ),
                  if (state.reportData != null && state.reportData!.campaignSummaries.isNotEmpty)
                    BlocBuilder<FilterCubit, FilterState>(
                        bloc: Modular.get<FilterCubit>(),
                        builder: (_, reportFilterState) {
                          return _buildSummary(state, reportFilterState);
                        }),
                  if (state.reportData != null && state.reportData!.campaignSummaries.isNotEmpty)
                    _buildConversionResultBody(state),
                  if (state.reportData != null && state.reportData!.campaignSummaries.isEmpty)
                    buildNoResultBody(
                      context,
                      () {
                        cubit.hideReport();
                        Modular.get<FilterCubit>().clear();
                      },
                    ),
                ],
              ),
            ),
          );
        }
        return buildAddFilterMessage(context);
      },
    );
  }

  Widget _buildSummary(ConversionReportState state, FilterState filterState) {
    return Padding(
      padding: EdgeInsets.only(left: 16.r, right: 16.r, top: 8.r),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(color: ColorConstants.borderColor, width: 1.r),
        ),
        child: IntrinsicHeight(
          child: Row(
            children: [
              _buildSummaryCard(
                  state.reportData!.campaignSummaries
                      .map((data) => data.conversionCount)
                      .reduce((a1, a2) => a1 + a2)
                      .toCommaSeparated(),
                  'Conversions',
                  'Descriptions',
                  filterState.selectedDateType),
              VerticalDivider(
                width: 1.r,
                color: ColorConstants.borderColor,
              ),
              _buildSummaryCard(
                  state.reportData!.campaignSummaries
                      .map((data) => data.totalReward)
                      .reduce((a1, a2) => a1 + a2)
                      .toPrice(state.currency),
                  'Reward',
                  'Descriptions',
                  filterState.selectedDateType),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSummaryCard(String number, String tagName, String tagDescription, ReportQueryPeriodBase dateType) {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.all(12.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              number,
              style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.w500),
            ),
            SizedBox(
              height: 4.r,
            ),
            Row(
              spacing: 4.r,
              children: [
                Text(
                  tagName,
                  style: Theme.of(context)
                      .textTheme
                      .labelLarge!
                      .copyWith(color: const Color(0xFF767676), fontWeight: FontWeight.w500),
                ),
                GestureDetector(
                    onTap: () {
                      showDescription(context, tagName, tagDescription);
                    },
                    child: Icon(
                      Icons.help_outline,
                      size: 12.r,
                    )),
              ],
            ),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 4.r, vertical: 2.r),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(9999),
                  color: dateType == ReportQueryPeriodBase.CONVERSION_DATE ? const Color(0xFF1AAA55) : Colors.red),
              child: Text(dateType == ReportQueryPeriodBase.CONVERSION_DATE ? 'Occurred' : 'Approved',
                  style: Theme.of(context)
                      .textTheme
                      .labelMedium!
                      .copyWith(color: Colors.white, fontWeight: FontWeight.w500)),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildConversionResultBody(ConversionReportState state) {
    return Padding(
      padding: EdgeInsets.all(16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // _buildPerformanceChart(),
          _buildConversionTable(state),
        ],
      ),
    );
  }
  Widget _buildConversionTable(ConversionReportState state) {
    return Theme(
      data: Theme.of(context).copyWith(
        cardTheme: const CardThemeData(color: Colors.white),
      ),
      child: PaginatedDataTable(
          headingRowColor: const WidgetStatePropertyAll(Color(0xFFF2F2F2)),
          rowsPerPage: 10,
          columns: [
            buildDataColumn(context, 'Campaign'),
            buildDataColumn(context, 'Conversions'),
            buildDataColumn(context, 'Reward'),
          ],
          onPageChanged: (int value) {
            if (value == 0) {
              _findConversions(1, 10);
              return;
            }
            _findConversions(((value / 10) + 1).toInt(), 10);
          },
          source: CommonDataTableSource(
              state.reportData!.campaignSummaries.map((conversion) {
                return DataRow(
                    color: WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
                      return Colors.white;
                    }),
                    cells: [
                      buildDataCell(context, conversion.campaignName, color: const Color(0xFFEF6507), onTap: () {
                        Modular.to.pushNamed('/report/campaign/details',
                            arguments: Item(name: conversion.campaignName, value: conversion.campaignId));
                      }),
                      buildDataCell(context, conversion.conversionCount.toCommaSeparated()),
                      buildDataCell(context, conversion.totalReward.toCommaSeparated()),
                    ]);
              }).toList(),
              state.reportData!.campaignCount)),
    );
  }

  Future<void> _findConversions(int page, int limit) async {
    cubit.showLoading();
    FilterState reportFilterState = Modular.get<FilterCubit>().state;
    DateTimeRange dateTimeRange =
        getTimeRange(reportFilterState.selectedPeriod, reportFilterState.startDate, reportFilterState.endDate);
    await cubit.findConversions(dateTimeRange.start, dateTimeRange.end, reportFilterState.selectedDateType, page, limit,
        reportFilterState.selectedStatus, reportFilterState.selectedCampaign, reportFilterState.selectedSite);
    Modular.get<FilterCubit>().selectRangeDate(dateTimeRange.start, dateTimeRange.end);
    cubit.hideLoading();
  }

  Future<void> _refreshData() async {
    final FilterState reportFilterState = Modular.get<FilterCubit>().state;

    if (reportFilterState != FilterState() && cubit.state.showReport) {
      final DateTimeRange dateTimeRange =
          getTimeRange(reportFilterState.selectedPeriod, reportFilterState.startDate, reportFilterState.endDate);

      await cubit.pullToRefresh(dateTimeRange.start, dateTimeRange.end, reportFilterState.selectedDateType, 1, 10,
          reportFilterState.selectedStatus, reportFilterState.selectedCampaign, reportFilterState.selectedSite);
    }
  }
}
