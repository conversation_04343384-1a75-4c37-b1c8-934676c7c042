import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/modules/account/cubit/site_cubit.dart';
import 'package:koc_app/src/modules/report/data/model/report_model.dart';
import 'package:koc_app/src/modules/shared/cubit/filter_cubit.dart';
import 'package:koc_app/src/modules/shared/cubit/filter_state.dart';
import 'package:koc_app/src/modules/shared/presentation/filter_page.dart';
import 'package:koc_app/src/shared/data/item.dart';

class ConversionFilterPage extends StatefulWidget {
  final List<ReportPeriod> periods;
  final bool showSites;

  const ConversionFilterPage(
    this.periods, {
    required this.showSites,
    super.key,
  });

  @override
  State<ConversionFilterPage> createState() => _ConversionFilterPageState();
}

class _ConversionFilterPageState extends State<ConversionFilterPage> {
  late FilterCubit cubit;
  late TextEditingController customNameController;
  late TextEditingController invoiceNumberController;

  @override
  void initState() {
    super.initState();
    cubit = Modular.get<FilterCubit>();
    customNameController = TextEditingController();
    invoiceNumberController = TextEditingController();
    initData();
  }

  Future<void> initData() async {
    if (cubit.state.customName.isNotEmpty) {
      customNameController.text = cubit.state.customName;
    }
    if (cubit.state.invoiceId.isNotEmpty) {
      invoiceNumberController.text = cubit.state.invoiceId;
    }

    if (cubit.state.campaigns.isEmpty) {
      cubit.showLoading();
      await cubit.initSearchCondition(widget.periods, true, defaultPeriod: ReportPeriod.LAST_7_DAYS);
      cubit.hideLoading();
    }

    final siteCubit = Modular.get<SiteCubit>();
    if (siteCubit.state.currentSiteId != 0 && cubit.state.selectedSite == null) {
      final currentSite = siteCubit.state.sites.firstWhere((site) => site.id == siteCubit.state.currentSiteId);
      cubit.selectSite(Item(value: currentSite.id, name: currentSite.name));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: FilterPage(
        widget.periods,
        true,
        true,
        widget.showSites,
        true,
        false,
        false,
        filterButtonName: 'Show report',
        preserveState: false,
      ),
      bottomSheet: Container(
        color: Colors.white,
        padding: EdgeInsets.all(16.r),
        child: Row(
          spacing: 16.r,
          children: [
            Expanded(
              child: SizedBox(
                height: 40.r,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20.r),
                          side: BorderSide(
                            width: 1.r,
                            color: const Color(0xFFAAAAAA),
                          )),
                      padding: EdgeInsets.symmetric(horizontal: 8.r)),
                  onPressed: _resetFilterWithCustomDefault,
                  child: Text('Clear',
                      style: Theme.of(context).textTheme.labelLarge!.copyWith(fontWeight: FontWeight.w500)),
                ),
              ),
            ),
            Expanded(
                child: BlocBuilder<FilterCubit, FilterState>(
                    bloc: cubit,
                    builder: (_, state) {
                      final bool isEnabled = (state.selectedPeriod != ReportPeriod.CUSTOM_RANGE ||
                              (state.selectedPeriod == ReportPeriod.CUSTOM_RANGE &&
                                  state.startDate != null &&
                                  state.endDate != null)) &&
                          state.isSearchEnabled;

                      return SizedBox(
                        height: 40.r,
                        child: ElevatedButton(
                          onPressed: isEnabled
                              ? () {
                                  Modular.to.pop(true);
                                }
                              : null,
                          child: Text(
                            'Show report',
                            style: Theme.of(context).textTheme.labelLarge!.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w500,
                                ),
                          ),
                        ),
                      );
                    })),
          ],
        ),
      ),
    );
  }

  void _resetFilterWithCustomDefault() async {
    customNameController.clear();
    invoiceNumberController.clear();

    await cubit.clearAndInitWithDefaultPeriod(widget.periods, true, ReportPeriod.LAST_7_DAYS);
  }

  @override
  void dispose() {
    customNameController.dispose();
    invoiceNumberController.dispose();
    super.dispose();
  }
}
