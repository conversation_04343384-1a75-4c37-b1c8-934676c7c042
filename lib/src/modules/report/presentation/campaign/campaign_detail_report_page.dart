import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/report/cubit/campaign/campaign_detail_report_cubit.dart';
import 'package:koc_app/src/modules/report/cubit/campaign/campaign_detail_report_state.dart';
import 'package:koc_app/src/modules/report/data/model/report_model.dart';
import 'package:koc_app/src/modules/report/mixin/report_mixin.dart';
import 'package:koc_app/src/modules/shared/cubit/filter_cubit.dart';
import 'package:koc_app/src/modules/shared/cubit/filter_state.dart';
import 'package:koc_app/src/shared/constants/color_constants.dart';
import 'package:koc_app/src/shared/data/item.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';
import 'package:koc_app/src/shared/widgets/pull_to_refresh_wrapper.dart';

class CampaignDetailReportPage extends StatefulWidget {
  final Item campaign;
  const CampaignDetailReportPage(this.campaign, {super.key});

  @override
  State<CampaignDetailReportPage> createState() => _CampaignDetailReportPageState();
}

class _CampaignDetailReportPageState extends BasePageState<CampaignDetailReportPage, CampaignDetailReportCubit>
    with ReportMixin {
  static const int pageSize = 10;
  late FilterCubit reportFilterCubit = Modular.get<FilterCubit>();

  @override
  void initState() {
    super.initState();
    doLoadingAction(() async {
      FilterState reportFilterState = reportFilterCubit.state;
      await cubit.findConversions(widget.campaign.value, reportFilterState.startDate!, reportFilterState.endDate!,
          reportFilterState.selectedDateType, reportFilterState.selectedSite,
          page: 1, pageSize: pageSize);
    });
  }

  /// Refresh data for pull-to-refresh
  Future<void> _refreshData() async {
    final FilterState reportFilterState = reportFilterCubit.state;
    final currentState = cubit.state;

    await cubit.pullToRefresh(widget.campaign.value, reportFilterState.startDate!, reportFilterState.endDate!,
        reportFilterState.selectedDateType, reportFilterState.selectedSite,
        page: currentState.currentPage, pageSize: currentState.pageSize);
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: Text(widget.campaign.name),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return PullToRefreshWrapper(
      onRefresh: _refreshData,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: BlocBuilder<CampaignDetailReportCubit, CampaignDetailReportState>(
              bloc: cubit,
              builder: (_, state) {
                if (state.reportData.isNotEmpty) {
                  return _buildDataTable(state);
                }
                return const SizedBox.shrink();
              }),
        ),
      ),
    );
  }

  Widget _buildDataTable(CampaignDetailReportState state) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: const Color(0x1F000000),
          width: 1.r,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12.r),
        child: Column(
          children: [
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                headingRowColor: const WidgetStatePropertyAll(Color(0xFFF2F2F2)),
                columnSpacing: 16.r,
                dataRowMinHeight: 56.r,
                dataRowMaxHeight: 56.r,
                columns: [
                  buildDataColumn(context, 'Transaction ID'),
                  buildDataColumn(context, 'Customer Type'),
                  buildDataColumn(context, 'Click Time'),
                  buildDataColumn(context, 'Conversion Time'),
                  buildDataColumn(context, 'Validated Time'),
                  buildDataColumn(context, 'Reward (${NumberFormat().simpleCurrencySymbol(state.currency)})'),
                  buildDataColumn(context, 'Status'),
                ],
                rows: [
                  ...state.reportData.map((row) => DataRow(cells: [
                    buildDataCell(context, row.verificationId, color: ColorConstants.textButtonColor, onTap: () {
                      Modular.to.pushNamed('/report/campaign/transaction', arguments: [widget.campaign.value, row]);
                    }),
                    buildDataCell(context, row.customerType ?? ''),
                    buildDataCell(context, row.clickTime?.toStandardDateTime() ?? ''),
                    buildDataCell(context, row.conversionTime?.toStandardDateTime() ?? ''),
                    buildDataCell(context, row.confirmationTime?.toStandardDateTime() ?? ''),
                    buildDataCell(context, row.reward.formatNumber()),
                    DataCell(
                      Container(
                        padding: EdgeInsets.only(left: 4.r, right: 4.r, top: 2.r, bottom: 2.r),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(9999),
                          color: _getColor(row.status),
                        ),
                        child: Text(
                          row.status.toString().split('.').last.toTitleCase(),
                          style: context.textLabelMedium(fontSize: 12.r, color: Colors.white, fontWeight: FontWeight.w500),
                        ),
                      ),
                    ),
                  ])),
                  if (state.reportData.length < pageSize)
                    ...List.generate(
                      pageSize - state.reportData.length,
                      (_) => DataRow(cells: List.generate(7, (_) => const DataCell(Text('')))),
                    ),
                ],
              ),
            ),
            _buildPaginationControls(state),
          ],
        ),
      ),
    );
  }

  Widget _buildPaginationControls(CampaignDetailReportState state) {
    final totalPages = (state.totalCount / state.pageSize).ceil();
    final currentPage = state.currentPage;
    final startItem = ((currentPage - 1) * state.pageSize) + 1;
    final endItem = (currentPage * state.pageSize).clamp(0, state.totalCount);

    return Container(
      height: 56.0,
      padding: EdgeInsets.symmetric(horizontal: 16.r),
      decoration: const BoxDecoration(
        border: Border(top: BorderSide(color: Color(0xFFE0E0E0), width: 1)),
        color: Colors.white,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Text(
            '$startItem–$endItem of ${state.totalCount}',
            style: Theme.of(context).textTheme.labelLarge
          ),
          SizedBox(width: 24.r),
          IconButton(
            onPressed: currentPage > 1 ? () => _goToPage(currentPage - 1) : null,
            icon: Icon(
              Icons.keyboard_arrow_left,
              color: currentPage > 1 ? Colors.grey[700] : Colors.grey[400],
            ),
            iconSize: 24.r,
            padding: EdgeInsets.all(8.r),
            constraints: BoxConstraints(
              minWidth: 40.r,
              minHeight: 40.r,
            ),
          ),
          IconButton(
            onPressed: currentPage < totalPages ? () => _goToPage(currentPage + 1) : null,
            icon: Icon(
              Icons.keyboard_arrow_right,
              color: currentPage < totalPages ? Colors.grey[700] : Colors.grey[400],
            ),
            iconSize: 24.r,
            padding: EdgeInsets.all(8.r),
            constraints: BoxConstraints(
              minWidth: 40.r,
              minHeight: 40.r,
            ),
          ),
        ],
      ),
    );
  }

  void _goToPage(int page) {
    doLoadingAction(() async {
      final FilterState reportFilterState = reportFilterCubit.state;

      await cubit.findConversions(widget.campaign.value, reportFilterState.startDate!, reportFilterState.endDate!,
          reportFilterState.selectedDateType, reportFilterState.selectedSite,
          page: page, pageSize: pageSize);
    });
  }

  Color _getColor(ConversionStatus status) {
    if (status == ConversionStatus.APPROVED) {
      return Colors.green;
    } else if (status == ConversionStatus.REJECTED) {
      return Colors.red;
    }
    return Colors.orange;
  }
}
