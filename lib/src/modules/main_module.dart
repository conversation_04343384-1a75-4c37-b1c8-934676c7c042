import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/app/cubit/app_cubit.dart';
import 'package:koc_app/src/base/cubit/common/common_cubit.dart';
import 'package:koc_app/src/modules/account/account_module.dart';
import 'package:koc_app/src/modules/account/cubit/account_cubit.dart';
import 'package:koc_app/src/modules/authentication/authentication_module.dart';
import 'package:koc_app/src/modules/campaign/campaign_module.dart';
import 'package:koc_app/src/modules/home/<USER>';
import 'package:koc_app/src/modules/information/terms_definition_page.dart';
import 'package:koc_app/src/modules/login_redirection_guard.dart';
import 'package:koc_app/src/modules/navigation/bottom_navigation_module.dart';
import 'package:koc_app/src/modules/notification/notification_module.dart';
import 'package:koc_app/src/modules/report/report_module.dart';
import 'package:koc_app/src/modules/shared/presentation/forgot_password_page.dart';
import 'package:koc_app/src/modules/shared/presentation/reset_password_page.dart';
import 'package:koc_app/src/modules/shared/presentation/verify_identity_page.dart';
import 'package:koc_app/src/modules/shared/shared_module.dart';

import 'package:koc_app/src/modules/survey/survey_module.dart';
import 'package:koc_app/src/shared/cache/cache_test_page.dart';
import 'package:koc_app/src/shared/debug/debug_page.dart';

class MainModule extends Module {
  @override
  List<Module> get imports => [SharedModule(), AccountSharedModule()];

  @override
  void binds(Injector i) {
    super.binds(i);
    i.addSingleton(AppCubit.new);
    i.addSingleton(CommonCubit.new);
  }

  @override
  void routes(RouteManager r) async {
    r.child('/terms-definition', child: (i) => const TermsDefinitionPage());
    r.child('/forgot-password',
        child: (i) => ForgotPasswordPage(
              r.args.data[0],
              arguments: (r.args.data as List).length > 1 ? r.args.data[1] : null,
            ));
    r.child('/reset-password', child: (i) => const ResetPasswordPage());
    r.child('/verify-identity/:verifyTarget',
        child: (i) => VerifyIdentityPage(
              verifyTarget: r.args.params['verifyTarget'],
            ));
    r.child('/cache-test', child: (i) => const CacheTestPage());
    r.child('/debug', child: (i) => const DebugPage());

    r.module('/', module: AuthenticationModule(), guards: [LoginRedirectGuard()]);
    r.module(
      '/survey',
      module: SurveyModule(), /*guards: [AuthGuard()]*/
    );
    r.module('/navigation', module: BottomNavigationModule() /* , guards: [AuthGuard()] */);
    r.module('/campaign', module: CampaignModule() /* , guards: [AuthGuard()] */);
    r.module('/home', module: HomeModule() /*, guards: [AuthGuard()]*/);
    r.module('/notification', module: NotificationModule() /*, guards: [AuthGuard()]*/);
    r.module('/account', module: AccountModule() /* , guards: [AuthGuard()] */);
    r.module('/report', module: ReportModule() /* , guards: [AuthGuard()] */);
  }
}
