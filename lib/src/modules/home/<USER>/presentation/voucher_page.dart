import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/home/<USER>/cubit/voucher_cubit.dart';
import 'package:koc_app/src/modules/home/<USER>/cubit/voucher_state.dart';
import 'package:koc_app/src/modules/home/<USER>/data/model/voucher.dart';
import 'package:koc_app/src/modules/home/<USER>/presentation/widget/voucher_card.dart';
import 'package:koc_app/src/shared/mixin/common_mixin.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';
import 'package:koc_app/src/shared/widgets/common_loading.dart';
import 'package:koc_app/src/shared/widgets/common_tab.dart';
import 'package:koc_app/src/shared/widgets/pull_to_refresh_wrapper.dart';

import '../../../../../generated/locale_keys.g.dart';
import '../../../../shared/services/shared_preferences_service.dart';

class VoucherPage extends StatefulWidget {
  const VoucherPage({super.key});

  @override
  State<VoucherPage> createState() => _VoucherPageState();
}

class _VoucherPageState extends BasePageState<VoucherPage, VoucherCubit>
    with TickerProviderStateMixin, CommonMixin {
  late TabController _tabController;
  late final SharedPreferencesService _sharedPrefsService;

  @override
  void initState() {
    super.initState();
    _sharedPrefsService = Modular.get<SharedPreferencesService>();
    loadVouchers();
    _tabController = TabController(length: 1, vsync: this);
    _setupTabController();
  }

  List<({int index, CommonTab tab})> _getVisibleTabs(VoucherState state) {
    int getPromoCount(String categoryId) {
      final category = state.categories.firstWhere(
        (cat) => cat.categoryId == categoryId,
        orElse: () => const VoucherCategory(categoryId: '', numberOfPromos: 0),
      );
      return category.numberOfPromos;
    }

    final tabData = <({int index, String title, int count})>[
      (index: 0, title: 'All', count: state.allVouchers.length),
      (
        index: 1,
        title: LocaleKeys.voucherCategory01.tr(),
        count: getPromoCount(VoucherCategoryType.travelHotel.categoryId)
      ),
      (
        index: 2,
        title: LocaleKeys.voucherCategory02.tr(),
        count: getPromoCount(VoucherCategoryType.electronics.categoryId)
      ),
      (
        index: 3,
        title: LocaleKeys.voucherCategory03.tr(),
        count: getPromoCount(VoucherCategoryType.fashion.categoryId)
      ),
      (
        index: 4,
        title: LocaleKeys.voucherCategory04.tr(),
        count: getPromoCount(VoucherCategoryType.beautyHealth.categoryId)
      ),
      (
        index: 5,
        title: LocaleKeys.voucherCategory05.tr(),
        count: getPromoCount(VoucherCategoryType.homeLiving.categoryId)
      ),
      (
        index: 6,
        title: LocaleKeys.voucherCategory06.tr(),
        count: getPromoCount(VoucherCategoryType.foodGrocery.categoryId)
      ),
    ];

    return tabData
        .where((tab) => tab.index == 0 || tab.count > 0)
        .map((tab) => (
          index: tab.index,
          tab: CommonTab(tab.title, count: tab.count)
        ))
        .toList();
  }


  void _setupTabController() {
    _tabController.addListener(() {
      if (!_tabController.indexIsChanging) {
        final visibleTabs = _getVisibleTabs(cubit.state);
        final currentTab = visibleTabs[_tabController.index];
        cubit.changeIndex(currentTab.index);
        _loadTabContent(currentTab.index);
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> loadVouchers() async {
    await _loadAllVouchers();
    await cubit.getVoucherCategories();
  }
Future<void> _loadAllVouchers() async {
  final siteId = await _sharedPrefsService.getCurrentSiteId();
  if (siteId == null) {
    return;
  }
  await cubit.findVouchers(FindVouchersRequest(siteId: siteId, type: 'ALL'));
}

Future<void> _loadTabContent(int originalIndex) async {
  final siteId = await _sharedPrefsService.getCurrentSiteId();
  if (siteId == null) return;


    try {
      String? categoryId;
      switch (originalIndex) {
        case 0:
          return; 
        case 1:
          categoryId = VoucherCategoryType.travelHotel.categoryId;
          break;
        case 2:
          categoryId = VoucherCategoryType.electronics.categoryId;
          break;
        case 3:
          categoryId = VoucherCategoryType.fashion.categoryId;
          break;
        case 4:
          categoryId = VoucherCategoryType.beautyHealth.categoryId;
          break;
        case 5:
          categoryId = VoucherCategoryType.homeLiving.categoryId;
          break;
        case 6:
          categoryId = VoucherCategoryType.foodGrocery.categoryId;
          break;
      }

      if (categoryId != null) {
        await cubit.findVouchers(FindVouchersRequest(siteId: siteId, category: categoryId));
      }
    } catch (e) {
      debugPrint('Error loading tab content: $e');
    }
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: buildSiteSelectionTitle(context, 'Voucher code', onTap: loadVouchers),
        customAction: IconButton(
          onPressed: () {
            Modular.to.pushNamed("/home/<USER>/search");
          },
          icon: Icon(
            Icons.search,
            size: 25.r,
          ),
        ),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return Column(
      children: [
        _buildTabBar(),
        _buildTabBarView(),
      ],
    );
  }

  void _updateTabController(List<({int index, CommonTab tab})> visibleTabs) {
    if (_tabController.length != visibleTabs.length) {
      final oldIndex = _tabController.index;
      _tabController.dispose();
      _tabController = TabController(
        length: visibleTabs.length,
        vsync: this,
        initialIndex: oldIndex < visibleTabs.length ? oldIndex : 0,
      );
      _setupTabController();
    }
  }

  Widget _buildTabBar() {
    return BlocConsumer<VoucherCubit, VoucherState>(
      bloc: cubit,
      listener: (context, state) {
        final visibleTabs = _getVisibleTabs(state);
        _updateTabController(visibleTabs);
      },
      builder: (context, state) {
        final visibleTabs = _getVisibleTabs(state);
        return TabBar(
          controller: _tabController,
          isScrollable: true,
          labelStyle: Theme.of(context).textTheme.labelLarge,
          tabAlignment: TabAlignment.start,
          indicatorColor: Colors.amber,
          labelColor: Colors.amber,
          tabs: visibleTabs.map((item) => item.tab).toList(),
        );
      },
    );
  }

  Widget _buildTabBarView() {
    return BlocConsumer<VoucherCubit, VoucherState>(
      bloc: cubit,
      listener: (context, state) {
        final visibleTabs = _getVisibleTabs(state);
        _updateTabController(visibleTabs);
      },
      builder: (context, state) {
        final visibleTabs = _getVisibleTabs(state);
        
        return Expanded(
          child: Padding(
            padding: EdgeInsets.all(16.r),
            child: TabBarView(
              controller: _tabController,
              children: visibleTabs.map((tabInfo) {
                switch (tabInfo.index) {
                  case 0:
                    return _buildVouchersWithLoading(
                      state.allVouchers,
                      state.isDataLoaded,
                      state.isPullToRefresh,
                      state.isSiteSwitching
                    );
                  case 1:
                    return _buildVouchersWithLoading(
                      state.travelAndHotels,
                      !state.loadingCampaignVouchers,
                      state.isPullToRefresh,
                      state.isSiteSwitching
                    );
                  case 2:
                    return _buildVouchersWithLoading(
                      state.electronics,
                      !state.loadingCampaignVouchers,
                      state.isPullToRefresh,
                      state.isSiteSwitching
                    );
                  case 3:
                    return _buildVouchersWithLoading(
                      state.fashion,
                      !state.loadingCampaignVouchers,
                      state.isPullToRefresh,
                      state.isSiteSwitching
                    );
                  case 4:
                    return _buildVouchersWithLoading(
                      state.beautyAndHealth,
                      !state.loadingCampaignVouchers,
                      state.isPullToRefresh,
                      state.isSiteSwitching
                    );
                  case 5:
                    return _buildVouchersWithLoading(
                      state.homeAndLiving,
                      !state.loadingCampaignVouchers,
                      state.isPullToRefresh,
                      state.isSiteSwitching
                    );
                  default:
                    return _buildVouchersWithLoading(
                      state.foodAndGrocery,
                      !state.loadingCampaignVouchers,
                      state.isPullToRefresh,
                      state.isSiteSwitching
                    );
                }
              }).toList(),
            ),
          ),
        );
      },
    );
  }

  Widget _buildVouchersWithLoading(List<Voucher> vouchers, bool isLoaded, bool isPullToRefresh, bool isSiteSwitching) {
    final shouldShowLoading = (!isLoaded && !isPullToRefresh) || isSiteSwitching;

    if (shouldShowLoading) {
      return const CommonLoading();
    }

    if (vouchers.isEmpty && isLoaded) {
      return PullToRefreshWrapper(
        onRefresh: () => cubit.pullToRefresh(),
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: SizedBox(
            height: MediaQuery.of(context).size.height * 0.6,
            child: Center(
              child: Container(
                padding: EdgeInsets.symmetric(vertical: 48.r),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const Icon(Icons.local_offer_outlined, color: Colors.grey, size: 48.0),
                    SizedBox(height: 12.r),
                    const Text(
                      'No coupons available',
                      style: TextStyle(fontSize: 16),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      );
    }

    return _buildVouchers(vouchers);
  }

  Widget _buildVouchers(List<Voucher> vouchers) {
    return PullToRefreshWrapper(
      onRefresh: () => cubit.pullToRefresh(),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(spacing: 8.r, children: vouchers.map((voucher) => VoucherCard(voucher)).toList()),
      ),
    );
  }
}
