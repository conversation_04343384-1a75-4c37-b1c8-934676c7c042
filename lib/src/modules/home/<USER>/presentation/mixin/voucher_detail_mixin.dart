import 'package:flutter/material.dart';
import 'package:koc_app/src/modules/home/<USER>/data/model/voucher.dart';
import 'package:koc_app/src/modules/home/<USER>/presentation/voucher_detail_page.dart';
import 'package:koc_app/src/shared/utils/modal_utils.dart';

mixin VoucherDetailMixin {
  void showVoucherDetailPage(BuildContext context, Voucher voucher) {
    ModalUtils.showDraggableBottomSheet(
      isScrollControlled: true,
      useSafeArea: true,
      initialHeight: 0.5,
      context: context,
      swipeUpToExpand: true,
      builder: (context, modalHeight) {
        return VoucherDetailPage(
          voucher: voucher,
          voucherId: voucher.id,
          modalheight: modalHeight,
        );
      },
    );
  }
}
