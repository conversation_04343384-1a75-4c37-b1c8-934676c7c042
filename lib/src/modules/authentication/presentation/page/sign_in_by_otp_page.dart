import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/modules/account/cubit/site_cubit.dart';
import 'package:koc_app/src/modules/authentication/data/models/otp.dart';
import 'package:koc_app/src/modules/authentication/presentation/page/base_sign_in_page.dart';
import 'package:koc_app/src/modules/shared/widget/otp_timer_field.dart';
import 'package:koc_app/src/shared/extensions.dart';

class SignInByOtpPage extends StatefulWidget {
  const SignInByOtpPage({super.key});

  @override
  State<SignInByOtpPage> createState() => _SignInByOtpPageState();
}

class _SignInByOtpPageState extends BaseSignInPageState<SignInByOtpPage> {
  late final SiteCubit siteCubit = Modular.get<SiteCubit>();

  @override
  Widget buildFooter() {
    return _buildOtpTimer<PERSON>ield();
  }

  Widget _buildOtpTimerField() {
    return Expanded(
      child: OtpT<PERSON><PERSON><PERSON><PERSON>(
          otpType: OtpType.signIn.name,
          verifyTarget: cubit.state.email,
          btnName: 'Log in',
          showLoading: () => commonCubit.showLoading(),
          hideLoading: () => commonCubit.hideLoading(),
          onTap: (value) async {
            commonCubit.showLoading();
            final result = await cubit.verifySignInOtpCode(cubit.state.email, value);
            if (result) {
              if (cubit.state.authTokenInfo?.token.isNotEmpty ?? false) {
                siteCubit.setSite(await cubit.commonCubit.sharedPreferencesService.getSites(),
                    await cubit.commonCubit.sharedPreferencesService.getCurrentSiteId() ?? 0);
                Modular.to.navigate("/navigation");
              }
            } else if (mounted) {
              context.showSnackBar(cubit.state.errorMessage);
            }
            commonCubit.hideLoading();
          }),
    );
  }
}
