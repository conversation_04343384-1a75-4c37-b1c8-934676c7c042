import 'dart:developer' as dev;

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/authentication/cubit/authentication_cubit.dart';
import 'package:koc_app/src/modules/authentication/cubit/authentication_state.dart';
import 'package:koc_app/src/modules/authentication/presentation/widget/social_login_button.dart';
import 'package:koc_app/src/modules/authentication/presentation/widget/text_link.dart';
import 'package:koc_app/src/modules/shared/model/country_selector_item.dart';
import 'package:koc_app/src/shared/constants/color_constants.dart';
import 'package:koc_app/src/shared/constants/country_constants.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/mixin/common_mixin.dart';
import 'package:koc_app/src/shared/services/google_auth_service.dart';

class SignInOrSignUpPage extends StatefulWidget {
  const SignInOrSignUpPage({super.key});

  @override
  State<SignInOrSignUpPage> createState() => _SignInOrSignUpPageState();
}

class _SignInOrSignUpPageState extends BasePageState<SignInOrSignUpPage, AuthenticationCubit> with CommonMixin {
  final TextEditingController _textEditingController = TextEditingController();
  bool _isGoogleLoading = false;
  bool _isFacebookLoading = false;

  @override
  void initState() {
    super.initState();
    _clearSocialLoginData();
  }

  Future<void> _clearSocialLoginData() async {
    try {
      await cubit.clearSocialLoginData();
    } catch (e) {
      dev.log('Error clearing social login data: $e');
    }
  }

  @override
  void dispose() {
    _textEditingController.dispose();
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      child: Container(
        height: 1.sh,
        padding: EdgeInsets.only(left: 16.r, right: 16.r),
        child: Column(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            SizedBox(height: 40.r),
            _buildMainContent(),
            _buildFooter(),
          ],
        ),
      ),
    );
  }

  Widget _buildMainContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildLogo(),
        SizedBox(height: 24.r),
        _buildTitle(),
        SizedBox(height: 48.r),
        _buildCountryHeader(),
        SizedBox(height: 8.r),
        SizedBox(height: 24.r),
        _buildEmailTextField(),
        SizedBox(height: 16.r),
        _buildContinueButton(),
        SizedBox(height: 20.r),
        _buildDivider(),
        SizedBox(height: 20.r),
        _buildSocialLoginButtons(),
      ],
    );
  }

  Widget _buildCountryHeader() {
    return Row(
      spacing: 4.r,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Confirm where you live: ',
              style: context.textBodySmall(fontWeight: FontWeight.w500, fontSize: 14.r),
            ),
            _buildCustomCountrySelector(),
          ],
        ),
        GestureDetector(
          onTap: () {
            showDescription(context, 'Confirm where you live',
                'Many brands give access to experts in specific regions. Please confirm your location to open access to more brands.');
          },
          child: Icon(
            Icons.help_outline,
            size: 16.r,
            color: ColorConstants.textColor,
          ),
        )
      ],
    );
  }

  Widget _buildCountryTitle() {
    return BlocBuilder<AuthenticationCubit, AuthenticationState>(
      bloc: cubit,
      builder: (_, state) {
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              state.country?.name ?? 'Country',
              style: context.textBodyMedium(
                color: ColorConstants.textButtonColor,
                fontWeight: FontWeight.w500,
                fontSize: 14.r,
              ),
            ),
            SizedBox(width: 4.r),
            Icon(
              Icons.keyboard_arrow_down,
              size: 20.r,
              color: ColorConstants.textButtonColor,
            )
          ],
        );
      },
    );
  }

  Widget _buildCustomCountrySelector() {
    return GestureDetector(
      onTap: () => _showCountrySelector(),
      child: _buildCountryTitle(),
    );
  }

  void _showCountrySelector() {
    showDialog(
      context: context,
      useSafeArea: true,
      builder: (BuildContext context) {
        return AlertDialog(
          contentPadding: EdgeInsets.only(left: 24.r, right: 12.r, top: 12.r, bottom: 24.r),
          backgroundColor: Colors.white,
          title: Text(
            'Country',
            style: context.textBodyMedium(),
          ),
          content: BlocBuilder<AuthenticationCubit, AuthenticationState>(
              bloc: cubit,
              builder: (_, state) {
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  spacing: 4.r,
                  children: CountryConstants.globalSignInCountries.map((e) {
                    return GestureDetector(
                      onTap: () {
                        cubit.selectCountry(e);
                        Navigator.of(context).pop();
                      },
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            e.name,
                            style: context.textBodySmall(),
                          ),
                          Radio<CountrySelectorItem>(
                            value: e,
                            groupValue: state.country,
                            activeColor: ColorConstants.selectedColor.withValues(alpha: 1),
                            onChanged: (CountrySelectorItem? value) {
                              if (value != null) {
                                cubit.selectCountry(value);
                                Navigator.of(context).pop();
                              }
                            },
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                );
              }),
        );
      },
    );
  }

  Widget _buildFooter() {
    TextStyle textStyle = context.textLabelLarge(fontWeight: FontWeight.w500, color: ColorConstants.textButtonColor);
    return Padding(
      padding: EdgeInsets.only(bottom: 24.r),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          TextLink(
            text: 'Terms of Service',
            url: 'https://accesstrade.global/terms-of-service',
            textStyle: textStyle,
          ),
          TextLink(
            text: 'Privacy Policy',
            url: 'https://accesstrade.global/privacy-policy',
            textStyle: textStyle,
          ),
          TextLink(
            text: 'Help',
            url: 'https://accesstrade.global/contact-us',
            textStyle: textStyle,
          ),
        ],
      ),
    );
  }

  Widget _buildContinueButton() {
    return BlocBuilder<AuthenticationCubit, AuthenticationState>(
      bloc: cubit,
      builder: (context, state) {
        return Center(
          child: SizedBox(
            height: 36.r,
            child: ElevatedButton(
              onPressed: state.isValidEmail
                  ? () async {
                      cubit.showButtonLoading();
                      await cubit.handleLoginOrSignup(_textEditingController.text);
                      final currentState = cubit.state;
                      final userExistCheck = currentState.userExistCheck;
                      final hasInactive = userExistCheck?.users.any((u) => u.isActive == false) ?? false;
                      if (hasInactive && context.mounted) {
                        showDialog(
                          context: context,
                          builder: (context) => AlertDialog(
                            backgroundColor: Colors.white,
                            title: Text(
                              'Your account has been deactivated',
                              textAlign: TextAlign.center,
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                            content: Text(
                              'Please contact us if you have questions or would like to reactivate your account.',
                              textAlign: TextAlign.center,
                              style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.w500),
                            ),
                            actionsAlignment: MainAxisAlignment.center,
                            actions: [
                              ElevatedButton(
                                onPressed: () => Navigator.of(context).pop(),
                                child: Text(
                                  'Got it',
                                  style: context.textBodySmall(color: Colors.white, fontWeight: FontWeight.w500),
                                ),
                              ),
                            ],
                          ),
                        );
                        cubit.hideButtonLoading();
                        return;
                      }
                      if (currentState.route.isNotEmpty) {
                        Modular.to.pushNamed(currentState.route);
                      } else if (context.mounted) {
                        context.showSnackBar(currentState.errorMessage);
                      }
                      cubit.hideButtonLoading();
                    }
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: ColorConstants.textButtonColor,
                disabledBackgroundColor: ColorConstants.disabledButtonColor,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25.r),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Continue',
                    style: context.textBodySmall(color: Colors.white, fontWeight: FontWeight.w500),
                  ),
                  if (state.isLoadingButton)
                    Padding(
                      padding: EdgeInsets.only(left: 8.r),
                      child: SizedBox(
                        width: 16.r,
                        height: 16.r,
                        child: const CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildEmailTextField() {
    return SizedBox(
      width: double.infinity,
      height: 36.r,
      child: TextField(
        controller: _textEditingController,
        style: Theme.of(context).textTheme.bodySmall,
        onChanged: (value) {
          cubit.validateEmail(value);
        },
        decoration: InputDecoration(
          hintText: 'Email',
          hintStyle: context.textBodySmall(color: ColorConstants.hintColor, fontSize: 14.r),
          filled: true,
          fillColor: Colors.white,
          contentPadding: EdgeInsets.symmetric(horizontal: 16.r, vertical: 12.r),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(25.r),
            borderSide: const BorderSide(color: ColorConstants.borderColor),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(25.r),
            borderSide: const BorderSide(color: ColorConstants.borderColor),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(25.r),
            borderSide: const BorderSide(color: ColorConstants.textButtonColor),
          ),
        ),
      ),
    );
  }

  Widget _buildDivider() {
    return Stack(
      alignment: Alignment.center,
      children: [
        Divider(color: ColorConstants.borderColor, height: 1.r),
        Container(
          color: Colors.white,
          padding: EdgeInsets.symmetric(horizontal: 15.r),
          child: Text(
            'OR',
            style: context.textLabelMedium(color: ColorConstants.hintColor, fontWeight: FontWeight.w500),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  Widget _buildSocialLoginButtons() {
    return BlocBuilder<AuthenticationCubit, AuthenticationState>(
      bloc: cubit,
      builder: (context, state) {
        const String countrySelectionMessage = 'Please select a country first';

        void checkCountryAndProceed(Function() onProceed) {
          if (state.country == null) {
            context.showSnackBar(countrySelectionMessage);
            return;
          }
          onProceed();
        }

        return Column(
          children: [
            SocialLoginButton(
              loginType: SocialLoginType.google,
              isLoading: _isGoogleLoading,
              onPressed: () {
                checkCountryAndProceed(() {
                  _handleGoogleSignIn();
                });
              },
            ),
            SizedBox(height: 16.r),
            SocialLoginButton(
              loginType: SocialLoginType.facebook,
              isLoading: _isFacebookLoading,
              onPressed: () {
                checkCountryAndProceed(() {
                  _handleFacebookSignIn();
                });
              },
            ),
          ],
        );
      },
    );
  }

  Future<void> _handleGoogleSignIn() async {
    setState(() {
      _isGoogleLoading = true;
    });
    try {
      final googleAuthService = Modular.get<GoogleAuthService>();
      final tokens = await googleAuthService.signInWithGoogle();

      if (!mounted) return;

      if (tokens != null) {
        final result = await cubit.handleGoogleSignIn(tokens);

        if (!mounted) return;

        if (result) {
          final currentState = cubit.state;
          if (currentState.route.isNotEmpty) {
            Modular.to.pushNamed(currentState.route);
          } else {
            Modular.to.navigate("/navigation");
          }
        } else {
          final errorMessage = cubit.state.errorMessage;
          if (errorMessage.isNotEmpty && mounted) {
            context.showSnackBar(errorMessage);
          }
        }
      } else {
        // User cancelled the sign-in process, no need to show an error message
      }
    } catch (e) {
      if (mounted) {
        context.showSnackBar('Failed to sign in with Google. Please try again.');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isGoogleLoading = false;
        });
      }
    }
  }

  Future<void> _handleFacebookSignIn() async {
    setState(() {
      _isFacebookLoading = true;
    });

    try {
      if (!mounted) return;

      final result = await cubit.initiateFacebookSignIn();

      if (!mounted) return;

      if (result.success) {
        if (result.navigateTo != null) {
          Modular.to.pushNamed(result.navigateTo!);
        }
      } else if (!result.cancelled && result.errorMessage != null && result.errorMessage!.isNotEmpty) {
        context.showSnackBar(result.errorMessage!);
      }
    } catch (e) {
      if (mounted) {
        context.showSnackBar('Failed to sign in with Facebook. Please try again.');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isFacebookLoading = false;
        });
      }
    }
  }

  Widget _buildTitle() {
    return Text(
      'signInOrCreateAccount'.tr(),
      style: Theme.of(context).textTheme.bodyLarge!.copyWith(fontWeight: FontWeight.w700, fontSize: 24.r),
    );
  }
}
