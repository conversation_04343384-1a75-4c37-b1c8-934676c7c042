import 'package:freezed_annotation/freezed_annotation.dart';

part 'user.freezed.dart';
part 'user.g.dart';

@freezed
class UserHasPassword with _$UserHasPassword {
  factory UserHasPassword({
    required String username,
    required bool hasPassword,
    @Default('') String firstName,
    @Default('') String lastName,
    @Default('') String profilePictureUrl,
    @Default(true) bool isActive,
  }) = _UserHasPassword;

  factory UserHasPassword.fromJson(Map<String, dynamic> json) => _$UserHasPasswordFromJson(json);
}

@freezed
class UserExistCheck with _$UserExistCheck {
  factory UserExistCheck({
    @Default(false) bool isEmailRegistered,
    @Default(false) bool isGlobal,
    @Default([]) List<UserHasPassword> users,
  }) = _UserExistCheck;

  factory UserExistCheck.fromJson(Map<String, dynamic> json) => _$UserExistCheckFromJson(json);
}

@freezed
class CheckEmailRequest with _$CheckEmailRequest {
  factory CheckEmailRequest({
    required String email,
    required String countryCode,
  }) = _CheckEmailRequest;

  factory CheckEmailRequest.fromJson(Map<String, dynamic> json) => _$CheckEmailRequestFromJson(json);
}
