// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'otp.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SendCodeOtpRequest _$SendCodeOtpRequestFromJson(Map<String, dynamic> json) {
  return _SendCodeOtpRequest.fromJson(json);
}

/// @nodoc
mixin _$SendCodeOtpRequest {
  String get email => throw _privateConstructorUsedError;
  String get countryCode => throw _privateConstructorUsedError;
  OtpType get otpType => throw _privateConstructorUsedError;

  /// Serializes this SendCodeOtpRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SendCodeOtpRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SendCodeOtpRequestCopyWith<SendCodeOtpRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SendCodeOtpRequestCopyWith<$Res> {
  factory $SendCodeOtpRequestCopyWith(
          SendCodeOtpRequest value, $Res Function(SendCodeOtpRequest) then) =
      _$SendCodeOtpRequestCopyWithImpl<$Res, SendCodeOtpRequest>;
  @useResult
  $Res call({String email, String countryCode, OtpType otpType});
}

/// @nodoc
class _$SendCodeOtpRequestCopyWithImpl<$Res, $Val extends SendCodeOtpRequest>
    implements $SendCodeOtpRequestCopyWith<$Res> {
  _$SendCodeOtpRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SendCodeOtpRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = null,
    Object? countryCode = null,
    Object? otpType = null,
  }) {
    return _then(_value.copyWith(
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      countryCode: null == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String,
      otpType: null == otpType
          ? _value.otpType
          : otpType // ignore: cast_nullable_to_non_nullable
              as OtpType,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SendCodeOtpRequestImplCopyWith<$Res>
    implements $SendCodeOtpRequestCopyWith<$Res> {
  factory _$$SendCodeOtpRequestImplCopyWith(_$SendCodeOtpRequestImpl value,
          $Res Function(_$SendCodeOtpRequestImpl) then) =
      __$$SendCodeOtpRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String email, String countryCode, OtpType otpType});
}

/// @nodoc
class __$$SendCodeOtpRequestImplCopyWithImpl<$Res>
    extends _$SendCodeOtpRequestCopyWithImpl<$Res, _$SendCodeOtpRequestImpl>
    implements _$$SendCodeOtpRequestImplCopyWith<$Res> {
  __$$SendCodeOtpRequestImplCopyWithImpl(_$SendCodeOtpRequestImpl _value,
      $Res Function(_$SendCodeOtpRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of SendCodeOtpRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = null,
    Object? countryCode = null,
    Object? otpType = null,
  }) {
    return _then(_$SendCodeOtpRequestImpl(
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      countryCode: null == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String,
      otpType: null == otpType
          ? _value.otpType
          : otpType // ignore: cast_nullable_to_non_nullable
              as OtpType,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SendCodeOtpRequestImpl implements _SendCodeOtpRequest {
  const _$SendCodeOtpRequestImpl(
      {required this.email, required this.countryCode, required this.otpType});

  factory _$SendCodeOtpRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$SendCodeOtpRequestImplFromJson(json);

  @override
  final String email;
  @override
  final String countryCode;
  @override
  final OtpType otpType;

  @override
  String toString() {
    return 'SendCodeOtpRequest(email: $email, countryCode: $countryCode, otpType: $otpType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SendCodeOtpRequestImpl &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.countryCode, countryCode) ||
                other.countryCode == countryCode) &&
            (identical(other.otpType, otpType) || other.otpType == otpType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, email, countryCode, otpType);

  /// Create a copy of SendCodeOtpRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SendCodeOtpRequestImplCopyWith<_$SendCodeOtpRequestImpl> get copyWith =>
      __$$SendCodeOtpRequestImplCopyWithImpl<_$SendCodeOtpRequestImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SendCodeOtpRequestImplToJson(
      this,
    );
  }
}

abstract class _SendCodeOtpRequest implements SendCodeOtpRequest {
  const factory _SendCodeOtpRequest(
      {required final String email,
      required final String countryCode,
      required final OtpType otpType}) = _$SendCodeOtpRequestImpl;

  factory _SendCodeOtpRequest.fromJson(Map<String, dynamic> json) =
      _$SendCodeOtpRequestImpl.fromJson;

  @override
  String get email;
  @override
  String get countryCode;
  @override
  OtpType get otpType;

  /// Create a copy of SendCodeOtpRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SendCodeOtpRequestImplCopyWith<_$SendCodeOtpRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

VerifySignInOtpRequest _$VerifySignInOtpRequestFromJson(
    Map<String, dynamic> json) {
  return _VerifySignInOtpRequest.fromJson(json);
}

/// @nodoc
mixin _$VerifySignInOtpRequest {
  String get email => throw _privateConstructorUsedError;
  String get countryCode => throw _privateConstructorUsedError;
  String get otp => throw _privateConstructorUsedError;
  OtpType get otpType => throw _privateConstructorUsedError;

  /// Serializes this VerifySignInOtpRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VerifySignInOtpRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VerifySignInOtpRequestCopyWith<VerifySignInOtpRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VerifySignInOtpRequestCopyWith<$Res> {
  factory $VerifySignInOtpRequestCopyWith(VerifySignInOtpRequest value,
          $Res Function(VerifySignInOtpRequest) then) =
      _$VerifySignInOtpRequestCopyWithImpl<$Res, VerifySignInOtpRequest>;
  @useResult
  $Res call({String email, String countryCode, String otp, OtpType otpType});
}

/// @nodoc
class _$VerifySignInOtpRequestCopyWithImpl<$Res,
        $Val extends VerifySignInOtpRequest>
    implements $VerifySignInOtpRequestCopyWith<$Res> {
  _$VerifySignInOtpRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VerifySignInOtpRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = null,
    Object? countryCode = null,
    Object? otp = null,
    Object? otpType = null,
  }) {
    return _then(_value.copyWith(
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      countryCode: null == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String,
      otp: null == otp
          ? _value.otp
          : otp // ignore: cast_nullable_to_non_nullable
              as String,
      otpType: null == otpType
          ? _value.otpType
          : otpType // ignore: cast_nullable_to_non_nullable
              as OtpType,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VerifySignInOtpRequestImplCopyWith<$Res>
    implements $VerifySignInOtpRequestCopyWith<$Res> {
  factory _$$VerifySignInOtpRequestImplCopyWith(
          _$VerifySignInOtpRequestImpl value,
          $Res Function(_$VerifySignInOtpRequestImpl) then) =
      __$$VerifySignInOtpRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String email, String countryCode, String otp, OtpType otpType});
}

/// @nodoc
class __$$VerifySignInOtpRequestImplCopyWithImpl<$Res>
    extends _$VerifySignInOtpRequestCopyWithImpl<$Res,
        _$VerifySignInOtpRequestImpl>
    implements _$$VerifySignInOtpRequestImplCopyWith<$Res> {
  __$$VerifySignInOtpRequestImplCopyWithImpl(
      _$VerifySignInOtpRequestImpl _value,
      $Res Function(_$VerifySignInOtpRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of VerifySignInOtpRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = null,
    Object? countryCode = null,
    Object? otp = null,
    Object? otpType = null,
  }) {
    return _then(_$VerifySignInOtpRequestImpl(
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      countryCode: null == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String,
      otp: null == otp
          ? _value.otp
          : otp // ignore: cast_nullable_to_non_nullable
              as String,
      otpType: null == otpType
          ? _value.otpType
          : otpType // ignore: cast_nullable_to_non_nullable
              as OtpType,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VerifySignInOtpRequestImpl implements _VerifySignInOtpRequest {
  const _$VerifySignInOtpRequestImpl(
      {required this.email,
      required this.countryCode,
      required this.otp,
      required this.otpType});

  factory _$VerifySignInOtpRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$VerifySignInOtpRequestImplFromJson(json);

  @override
  final String email;
  @override
  final String countryCode;
  @override
  final String otp;
  @override
  final OtpType otpType;

  @override
  String toString() {
    return 'VerifySignInOtpRequest(email: $email, countryCode: $countryCode, otp: $otp, otpType: $otpType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VerifySignInOtpRequestImpl &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.countryCode, countryCode) ||
                other.countryCode == countryCode) &&
            (identical(other.otp, otp) || other.otp == otp) &&
            (identical(other.otpType, otpType) || other.otpType == otpType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, email, countryCode, otp, otpType);

  /// Create a copy of VerifySignInOtpRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VerifySignInOtpRequestImplCopyWith<_$VerifySignInOtpRequestImpl>
      get copyWith => __$$VerifySignInOtpRequestImplCopyWithImpl<
          _$VerifySignInOtpRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VerifySignInOtpRequestImplToJson(
      this,
    );
  }
}

abstract class _VerifySignInOtpRequest implements VerifySignInOtpRequest {
  const factory _VerifySignInOtpRequest(
      {required final String email,
      required final String countryCode,
      required final String otp,
      required final OtpType otpType}) = _$VerifySignInOtpRequestImpl;

  factory _VerifySignInOtpRequest.fromJson(Map<String, dynamic> json) =
      _$VerifySignInOtpRequestImpl.fromJson;

  @override
  String get email;
  @override
  String get countryCode;
  @override
  String get otp;
  @override
  OtpType get otpType;

  /// Create a copy of VerifySignInOtpRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VerifySignInOtpRequestImplCopyWith<_$VerifySignInOtpRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

VerifySignUpOtpRequest _$VerifySignUpOtpRequestFromJson(
    Map<String, dynamic> json) {
  return _VerifySignUpOtpRequest.fromJson(json);
}

/// @nodoc
mixin _$VerifySignUpOtpRequest {
  String get email => throw _privateConstructorUsedError;
  String get countryCode => throw _privateConstructorUsedError;
  String get otp => throw _privateConstructorUsedError;
  OtpType get otpType => throw _privateConstructorUsedError;

  /// Serializes this VerifySignUpOtpRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VerifySignUpOtpRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VerifySignUpOtpRequestCopyWith<VerifySignUpOtpRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VerifySignUpOtpRequestCopyWith<$Res> {
  factory $VerifySignUpOtpRequestCopyWith(VerifySignUpOtpRequest value,
          $Res Function(VerifySignUpOtpRequest) then) =
      _$VerifySignUpOtpRequestCopyWithImpl<$Res, VerifySignUpOtpRequest>;
  @useResult
  $Res call({String email, String countryCode, String otp, OtpType otpType});
}

/// @nodoc
class _$VerifySignUpOtpRequestCopyWithImpl<$Res,
        $Val extends VerifySignUpOtpRequest>
    implements $VerifySignUpOtpRequestCopyWith<$Res> {
  _$VerifySignUpOtpRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VerifySignUpOtpRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = null,
    Object? countryCode = null,
    Object? otp = null,
    Object? otpType = null,
  }) {
    return _then(_value.copyWith(
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      countryCode: null == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String,
      otp: null == otp
          ? _value.otp
          : otp // ignore: cast_nullable_to_non_nullable
              as String,
      otpType: null == otpType
          ? _value.otpType
          : otpType // ignore: cast_nullable_to_non_nullable
              as OtpType,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VerifySignUpOtpRequestImplCopyWith<$Res>
    implements $VerifySignUpOtpRequestCopyWith<$Res> {
  factory _$$VerifySignUpOtpRequestImplCopyWith(
          _$VerifySignUpOtpRequestImpl value,
          $Res Function(_$VerifySignUpOtpRequestImpl) then) =
      __$$VerifySignUpOtpRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String email, String countryCode, String otp, OtpType otpType});
}

/// @nodoc
class __$$VerifySignUpOtpRequestImplCopyWithImpl<$Res>
    extends _$VerifySignUpOtpRequestCopyWithImpl<$Res,
        _$VerifySignUpOtpRequestImpl>
    implements _$$VerifySignUpOtpRequestImplCopyWith<$Res> {
  __$$VerifySignUpOtpRequestImplCopyWithImpl(
      _$VerifySignUpOtpRequestImpl _value,
      $Res Function(_$VerifySignUpOtpRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of VerifySignUpOtpRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = null,
    Object? countryCode = null,
    Object? otp = null,
    Object? otpType = null,
  }) {
    return _then(_$VerifySignUpOtpRequestImpl(
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      countryCode: null == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String,
      otp: null == otp
          ? _value.otp
          : otp // ignore: cast_nullable_to_non_nullable
              as String,
      otpType: null == otpType
          ? _value.otpType
          : otpType // ignore: cast_nullable_to_non_nullable
              as OtpType,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VerifySignUpOtpRequestImpl implements _VerifySignUpOtpRequest {
  const _$VerifySignUpOtpRequestImpl(
      {required this.email,
      required this.countryCode,
      required this.otp,
      required this.otpType});

  factory _$VerifySignUpOtpRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$VerifySignUpOtpRequestImplFromJson(json);

  @override
  final String email;
  @override
  final String countryCode;
  @override
  final String otp;
  @override
  final OtpType otpType;

  @override
  String toString() {
    return 'VerifySignUpOtpRequest(email: $email, countryCode: $countryCode, otp: $otp, otpType: $otpType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VerifySignUpOtpRequestImpl &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.countryCode, countryCode) ||
                other.countryCode == countryCode) &&
            (identical(other.otp, otp) || other.otp == otp) &&
            (identical(other.otpType, otpType) || other.otpType == otpType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, email, countryCode, otp, otpType);

  /// Create a copy of VerifySignUpOtpRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VerifySignUpOtpRequestImplCopyWith<_$VerifySignUpOtpRequestImpl>
      get copyWith => __$$VerifySignUpOtpRequestImplCopyWithImpl<
          _$VerifySignUpOtpRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VerifySignUpOtpRequestImplToJson(
      this,
    );
  }
}

abstract class _VerifySignUpOtpRequest implements VerifySignUpOtpRequest {
  const factory _VerifySignUpOtpRequest(
      {required final String email,
      required final String countryCode,
      required final String otp,
      required final OtpType otpType}) = _$VerifySignUpOtpRequestImpl;

  factory _VerifySignUpOtpRequest.fromJson(Map<String, dynamic> json) =
      _$VerifySignUpOtpRequestImpl.fromJson;

  @override
  String get email;
  @override
  String get countryCode;
  @override
  String get otp;
  @override
  OtpType get otpType;

  /// Create a copy of VerifySignUpOtpRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VerifySignUpOtpRequestImplCopyWith<_$VerifySignUpOtpRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
