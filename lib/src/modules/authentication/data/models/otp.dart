import 'package:freezed_annotation/freezed_annotation.dart';

part 'otp.freezed.dart';
part 'otp.g.dart';

enum OtpType {
  @JsonValue("REGISTRATION")
  registration,
  @JsonValue("CHANGE_PASSWORD")
  changePassword,
  @JsonValue("SIGN_IN")
  signIn,
  @JsonValue("SHOW_PAYMENT")
  showPayment
}

@freezed
class SendCodeOtpRequest with _$SendCodeOtpRequest {
  const factory SendCodeOtpRequest({
    required String email,
    required String countryCode,
    required OtpType otpType
  }) = _SendCodeOtpRequest;

  factory SendCodeOtpRequest.fromJson(Map<String, dynamic> json) =>
      _$SendCodeOtpRequestFromJson(json);
}

@freezed
class VerifySignInOtpRequest with _$VerifySignInOtpRequest {
  const factory VerifySignInOtpRequest({
    required String email,
    required String countryCode,
    required String otp,
    required OtpType otpType,
  }) = _VerifySignInOtpRequest;

  factory VerifySignInOtpRequest.fromJson(Map<String, dynamic> json) =>
      _$VerifySignInOtpRequestFromJson(json);
}

@freezed
class VerifySignUpOtpRequest with _$VerifySignUpOtpRequest {
  const factory VerifySignUpOtpRequest({
    required String email,
    required String countryCode,
    required String otp,
    required OtpType otpType,
  }) = _VerifySignUpOtpRequest;

  factory VerifySignUpOtpRequest.fromJson(Map<String, dynamic> json) =>
      _$VerifySignUpOtpRequestFromJson(json);
}