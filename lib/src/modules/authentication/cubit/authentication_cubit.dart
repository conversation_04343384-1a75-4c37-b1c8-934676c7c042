import 'dart:convert';
import 'dart:developer' as dev;

import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/account/data/model/publisher_sites.dart';
import 'package:koc_app/src/modules/account/data/repository/account_repository.dart';
import 'package:koc_app/src/modules/authentication/cubit/authentication_state.dart';
import 'package:koc_app/src/modules/authentication/data/models/auth_token_info.dart';
import 'package:koc_app/src/modules/authentication/data/models/otp.dart';
import 'package:koc_app/src/modules/authentication/data/models/sign_in_sign_up.dart';
import 'package:koc_app/src/modules/authentication/data/models/user.dart';
import 'package:koc_app/src/modules/authentication/data/repository/authentication_repository.dart';
import 'package:koc_app/src/modules/shared/cubit/otp_timer_cubit.dart';
import 'package:koc_app/src/modules/shared/model/country_selector_item.dart';
import 'package:koc_app/src/modules/shared/repositories/currency_repository.dart';
import 'package:koc_app/src/shared/cache/warm_cache_service.dart';
import 'package:koc_app/src/shared/constants/instance_key.dart';
import 'package:koc_app/src/shared/services/facebook_auth_service.dart';
import 'package:koc_app/src/shared/services/google_auth_service.dart';
import 'package:koc_app/src/shared/validator/validators.dart';

import '../../../shared/services/shared_preferences_service.dart';
import '../../../shared/utils/handle_error.dart';

class AuthenticationCubit extends BaseCubit<AuthenticationState> {
  final AuthenticationRepository _authenticationRepository;
  final AccountRepository _accountRepository;
  final CurrencyRepository _currencyRepository;
  final SharedPreferencesService sharedPreferencesService;
  static const storage = FlutterSecureStorage();

  AuthenticationCubit(
      this._authenticationRepository, this._accountRepository, this._currencyRepository, this.sharedPreferencesService)
      : super(AuthenticationState());

  void selectCountry(CountrySelectorItem country) {
    emit(state.copyWith(country: country));
  }

  Future<UserExistCheck?> checkUserExisting(
    String email, {
    bool updateState = false,
  }) async {
    try {
      showLoading();

      final countryCode = state.country?.country?.code;
      if (countryCode == null) {
        emit(state.copyWith(errorMessage: "Please select country"));
        return null;
      }

      final userExistCheckResponse = await _authenticationRepository.checkUserExistingBy(email, countryCode);
      if (userExistCheckResponse == null) {
        emit(state.copyWith(errorMessage: "Invalid response from server"));
        return null;
      }

      final userExistCheck = UserExistCheck.fromJson(userExistCheckResponse);

      if (updateState) {
        emit(state.copyWith(userExistCheck: userExistCheck));
      }

      await Future.wait([
        sharedPreferencesService.setEmail(email),
        sharedPreferencesService.setToInstance(InstanceConstants.countryCodeKey, countryCode),
        sharedPreferencesService.setToInstance(
            InstanceConstants.isEmailRegisteredKey, userExistCheck.isEmailRegistered),
        sharedPreferencesService.setToInstance(InstanceConstants.isGlobalKey, userExistCheck.isGlobal)
      ]);

      return userExistCheck;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return null;
    } finally {
      hideLoading();
    }
  }

  Future<void> handleLoginOrSignup(String email) async {
    final userExistCheck = await checkUserExisting(email, updateState: true);
    if (userExistCheck == null) {
      return;
    }

    final countryCode = state.country?.country?.code;
    if (countryCode == null) {
      emit(state.copyWith(errorMessage: "Country code is not selected"));
      return;
    }

    try {
      String route;

      if (userExistCheck.isEmailRegistered) {
        if (!userExistCheck.users.first.hasPassword) {
          await sendOtpSignIn(email, countryCode);
          route = '/sign-in-by-otp/';
        } else {
          route = '/sign-in-by-password/';
        }
      } else {
        await sendOtpRegister(email, countryCode);
        route = '/sign-up/';
      }

      emit(state.copyWith(route: route, email: email));
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  void toggleObscurePassword() {
    emit(state.copyWith(obscurePassword: !state.obscurePassword));
  }

  void setPasswordValid(bool valid) {
    emit(state.copyWith(isValidPassword: valid));
  }

  Future<void> sendOtpSignIn(String email, String countryCode) async {
    await _authenticationRepository.sendOtpSignIn(email, countryCode);
  }

  Future<void> sendOtpRegister(String email, String countryCode) async {
    SendCodeOtpRequest request = SendCodeOtpRequest(
      email: email,
      countryCode: countryCode,
      otpType: OtpType.registration
    );
    await _authenticationRepository.sendOtpRegister(request);
  }

  Future<void> verifySignUpOtpCode(String otp) async {
    try {
      final countryCode = await sharedPreferencesService.getCountryCode();
      if (countryCode == null) {
        emit(state.copyWith(errorMessage: "Country code is not selected"));
        return;
      }
      VerifySignUpOtpRequest request = VerifySignUpOtpRequest(
        email: state.email,
        countryCode: countryCode,
        otp: otp,
        otpType: OtpType.registration
      );
      await _authenticationRepository.verifySignUpOtpCode(request);
      emit(state.copyWith(route: '/survey/user'));
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  Future<bool> verifySignInOtpCode(String email, String otp) async {
    try {
      final countryCode = await sharedPreferencesService.getCountryCode();
      if (countryCode == null) {
        emit(state.copyWith(errorMessage: "Country code is not selected"));
        return false;
      }
      final result = await _authenticationRepository.verifySignInOtpCode(email, countryCode, otp);
      final authData = AuthTokenInfo.fromJson(result);
      emit(state.copyWith(authTokenInfo: authData));
      await Future.wait([commonCubit.saveToken(authData), getSites(), getCurrency()]);
      return true;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return false;
    }
  }

  Future<bool> verifyEmailPassword(String email, String password) async {
    try {
      final countryCode = await sharedPreferencesService.getCountryCode();
      if (countryCode == null) {
        emit(state.copyWith(errorMessage: "Country code is not selected"));
        return false;
      }
      final result = await _authenticationRepository.verifyEmailPassword(email, password, countryCode);
      final authData = AuthTokenInfo.fromJson(result);
      emit(state.copyWith(authTokenInfo: authData));
      await Future.wait([commonCubit.saveToken(authData), getSites(), getCurrency()]);
      return true;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return false;
    }
  }

  Future<void> getSites() async {
    try {
      final result = await _accountRepository.getSites();
      List<PublisherSite> sites = (result as List).map((item) => PublisherSite.fromJson(item)).toList();
      if (sites.isNotEmpty) {
        await sharedPreferencesService.setCurrentSiteId(sites.first.id);
        await sharedPreferencesService.setSites(sites);

        WarmCacheService().warmCacheAfterAuth().catchError((e) {
          dev.log('Cache warming after auth failed: $e');
        });
      }
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  Future<void> getCurrency() async {
    try {
      final result = await _currencyRepository.getCurrency();
      await sharedPreferencesService.setPublisherCurrency(result);
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  void validateEmail(String email) {
    emit(state.copyWith(
      isValidEmail: Validators.isValidEmail(email),
    ));
  }

  Future<bool> handleGoogleSignIn(GoogleSignInTokens tokens) async {
    try {
      showLoading();

      if (tokens.accessToken == null) {
        emit(state.copyWith(errorMessage: "Google sign-in failed. Please try again."));
        return false;
      }

      final userExistCheck = await checkUserExisting(tokens.email, updateState: true);
      if (userExistCheck == null) {
        return false;
      }

      final countryCode = state.country?.country?.code;
      if (countryCode == null) {
        emit(state.copyWith(errorMessage: "Country code is not selected"));
        return false;
      }

      final request = SocialSignInPayload(
          accessToken: tokens.accessToken!,
          countryCode: countryCode,
          socialNetworkType: SocialNetworkType.google.value);

      if (userExistCheck.isEmailRegistered) {
        final result = await _authenticationRepository.socialLogin(request);
        final authData = AuthTokenInfo.fromJson(result);
        emit(state.copyWith(authTokenInfo: authData));
        await Future.wait([commonCubit.saveToken(authData), getSites(), getCurrency()]);
      } else {
        if (tokens.displayName != null) {
          final nameParts = tokens.displayName!.split(' ');
          final firstName = nameParts.isNotEmpty ? nameParts.first : '';
          final lastName = nameParts.length > 1 ? nameParts.sublist(1).join(' ') : '';

          await Future.wait([
            sharedPreferencesService.setToInstance(InstanceConstants.firstNameKey, firstName),
            sharedPreferencesService.setToInstance(InstanceConstants.lastNameKey, lastName),
          ]);

          if (tokens.photoUrl != null) {
            await sharedPreferencesService.setToInstance(InstanceConstants.profilePictureUrlKey, tokens.photoUrl);
          }
        }
        emit(state.copyWith(route: '/survey/user'));
      }
      return true;
    } catch (e) {
      emit(state.copyWith(errorMessage: 'Login failed. Please try again.'));
      return false;
    } finally {
      hideLoading();
    }
  }

  Future<GoogleSignInTokens?> getGoogleSignInTokensFromStorage() async {
    try {
      final googleAuthData = await storage.read(key: InstanceConstants.googleAuthDataKey);
      if (googleAuthData != null) {
        final data = jsonDecode(googleAuthData);
        return GoogleSignInTokens(
          accessToken: data['accessToken'],
          idToken: data['idToken'],
          email: data['email'],
          displayName: data['displayName'],
          photoUrl: data['photoUrl'],
        );
      } else {
        return null;
      }
    } catch (e) {
      emit(state.copyWith(errorMessage: 'Failed to retrieve Google sign-in tokens.'));
      return null;
    }
  }

  /// **Facebook Authentication Flow - Step 1: Initial Sign-In**
  ///
  /// Handles the initial Facebook sign-in process.
  /// This method is called when a user starts the Facebook sign-in flow.
  /// It checks if the user has an existing account and navigates to the appropriate flow.
  ///
  /// **Use Case**: Called when user starts Facebook sign-in process
  ///
  /// **Parameters**: None
  ///
  /// **Returns**:
  /// - `FacebookSignInResult` object containing:
  ///   - `success`: Whether sign-in was successful
  ///   - `cancelled`: Whether sign-in was cancelled by the user
  ///   - `errorMessage`: Error message if sign-in fails
  ///   - `navigateTo`: Route to navigate to after sign-in

  Future<FacebookSignInResult> initiateFacebookSignIn() async {
    try {
      emit(state.copyWith(errorMessage: ''));
      showLoading();

      final facebookAuthService = Modular.get<FacebookAuthService>();
      final facebookResponse = await facebookAuthService.signInWithFacebook();

      if (facebookResponse == null) {
        return const FacebookSignInResult(success: false, cancelled: true);
      }

      if (!await _validateFacebookToken(facebookResponse)) {
        return const FacebookSignInResult(
            success: false,
            errorMessage: 'Facebook authentication failed. Please try again or use another login method.');
      }

      final isAccountLinked = await _checkIsAccountLinked(facebookResponse);

      if (isAccountLinked) {
        final loginSuccess = await _performFacebookLogin(facebookResponse);
        if (loginSuccess) {
          return const FacebookSignInResult(success: true, navigateTo: "/navigation");
        } else {
          return FacebookSignInResult(
            success: false,
            errorMessage:
                state.errorMessage.isNotEmpty ? state.errorMessage : 'Facebook login failed. Please try again.',
          );
        }
      }

      if (facebookResponse.email != null && facebookResponse.email!.isNotEmpty) {
        return await _handleNewFacebookAccount(facebookResponse);
      } else {
        return const FacebookSignInResult(success: true, navigateTo: '/facebook-email-verification/');
      }
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return const FacebookSignInResult(
          success: false, errorMessage: 'Failed to sign in with Facebook. Please try again.');
    } finally {
      hideLoading();
    }
  }

  Future<bool> _checkIsAccountLinked(FacebookAuthResponse facebookResponse) async {
    try {
      final countryCode = state.country?.country?.code;
      if (countryCode == null) {
        return false;
      }

      final socialSignInPayload = SocialSignInPayload(
        accessToken: facebookResponse.accessToken!,
        countryCode: countryCode,
        socialNetworkType: SocialNetworkType.facebook.value,
        isCheckExistingUser: true,
      );

      final result = await _authenticationRepository.socialLogin(socialSignInPayload);

      return result?.isNotEmpty ?? false;
    } catch (e) {
      dev.log('Error checking account existence: $e');
      return false;
    }
  }

  /// **Facebook Authentication - Perform Login**
  ///
  /// Performs the actual Facebook login after account existence has been verified.
  /// This method handles the authentication and token storage.
  ///
  /// **Parameters**:
  /// - `facebookResponse`: Facebook authentication response containing access token
  ///
  /// **Returns**:
  /// - `true` if login successful and user is authenticated
  /// - `false` if login fails due to token issues, network errors, or backend validation
  Future<bool> _performFacebookLogin(FacebookAuthResponse facebookResponse) async {
    try {
      final countryCode = state.country?.country?.code;
      if (countryCode == null) {
        emit(state.copyWith(errorMessage: "Country code is not selected"));
        return false;
      }

      final socialSignInPayload = SocialSignInPayload(
        accessToken: facebookResponse.accessToken!,
        countryCode: countryCode,
        socialNetworkType: SocialNetworkType.facebook.value,
        isCheckExistingUser: true,
      );

      final result = await _authenticationRepository.socialLogin(socialSignInPayload);
      if (result == null || result.isEmpty) {
        return false;
      }

      final authData = AuthTokenInfo.fromJson(result);
      emit(state.copyWith(authTokenInfo: authData));
      await Future.wait([commonCubit.saveToken(authData), getSites(), getCurrency()]);
      return true;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return false;
    }
  }

  /// **Facebook Authentication - Handle New Account**
  ///
  /// Handles Facebook authentication for accounts that don't exist in the system yet.
  /// Determines the appropriate flow based on email availability and user registration status.
  ///
  /// **Parameters**:
  /// - `facebookResponse`: Facebook authentication response containing user data
  ///
  /// **Returns**:
  /// - `FacebookSignInResult` indicating the next step in the authentication flow
  Future<FacebookSignInResult> _handleNewFacebookAccount(FacebookAuthResponse facebookResponse) async {
    final userExistCheck = await checkUserExisting(
      facebookResponse.email!,
      updateState: true,
    );

    if (userExistCheck == null) {
      return FacebookSignInResult(
        success: false,
        errorMessage: state.errorMessage.isNotEmpty ? state.errorMessage : 'Failed to verify user information.',
      );
    }

    if (userExistCheck.isEmailRegistered) {
      if (facebookResponse.accessToken != null && facebookResponse.accessToken!.isNotEmpty) {
        final loginResult = await authenticateExistingFacebookUser(facebookResponse.accessToken!);
        if (loginResult) {
          return const FacebookSignInResult(success: true, navigateTo: "/navigation");
        } else {
          return FacebookSignInResult(
              success: false,
              errorMessage:
                  state.errorMessage.isNotEmpty ? state.errorMessage : 'Facebook login failed. Please try again.');
        }
      } else {
        return const FacebookSignInResult(
            success: false, errorMessage: 'Facebook access token is invalid. Please try again.');
      }
    } else {
      await _storeFacebookUserData(facebookResponse);
      return const FacebookSignInResult(success: true, navigateTo: '/survey/user');
    }
  }

  /// **Facebook Authentication - Store User Data**
  ///
  /// Stores Facebook user data (name, profile picture, etc.) to SharedPreferences
  /// for new users before proceeding to survey.
  ///
  /// **Parameters**:
  /// - `facebookResponse`: Facebook authentication response containing user data
  Future<void> _storeFacebookUserData(FacebookAuthResponse facebookResponse) async {
    try {
      if (facebookResponse.first_name != null && facebookResponse.last_name != null) {
        await Future.wait([
          sharedPreferencesService.setToInstance(InstanceConstants.firstNameKey, facebookResponse.first_name!),
          sharedPreferencesService.setToInstance(InstanceConstants.lastNameKey, facebookResponse.last_name!),
          sharedPreferencesService.setToInstance(InstanceConstants.facebookIdKey, facebookResponse.id),
        ]);

        if (facebookResponse.picture != null && facebookResponse.picture!.data.url.isNotEmpty) {
          await sharedPreferencesService.setToInstance(
              InstanceConstants.profilePictureUrlKey, facebookResponse.picture!.data.url);
        }
      }
    } catch (e) {
      dev.log('Error storing Facebook user data: $e');
    }
  }

  /// **Facebook Authentication Flow - Step 2: Email Verification**
  ///
  /// Handles the email verification process when Facebook doesn't share the user's email.
  /// Determines if user exists, sends appropriate OTP, and prepares for verification.
  ///
  /// **Use Case**: Called when user enters email on Facebook email verification page
  ///
  /// **Parameters**:
  /// - `email`: Email address entered by the user for verification
  ///
  /// **Returns**:
  /// - `FacebookEmailVerificationResult` object containing:
  ///   - `success`: Whether email verification setup was successful
  ///   - `isEmailRegistered`: Whether user has existing account
  ///   - `errorMessage`: Error details (if failed)
  ///   - `email`: Processed email address

  Future<FacebookEmailVerificationResult> processFacebookEmailVerification(String email) async {
    try {
      emit(state.copyWith(errorMessage: ''));

      if (!Validators.isValidEmail(email)) {
        return FacebookEmailVerificationResult(
          success: false,
          isEmailRegistered: false,
          errorMessage: "Please enter a valid email address",
          email: email,
        );
      }

      final userExistCheck = await checkUserExisting(email);

      if (userExistCheck == null) {
        return FacebookEmailVerificationResult(
          success: false,
          isEmailRegistered: false,
          errorMessage: state.errorMessage.isNotEmpty ? state.errorMessage : "Failed to verify user information",
          email: email,
        );
      }

      final countryCodeError = await _validateCountryCode();
      if (countryCodeError != null) {
        return FacebookEmailVerificationResult(
          success: false,
          isEmailRegistered: userExistCheck.isEmailRegistered,
          errorMessage: countryCodeError,
          email: email,
        );
      }

      final countryCode = await sharedPreferencesService.getCountryCode();

      await _storeEmailAndRegistrationStatus(email, userExistCheck.isEmailRegistered);

      if (userExistCheck.isEmailRegistered) {
        await sendOtpSignIn(email, countryCode!);
      } else {
        await sendOtpRegister(email, countryCode!);
      }

      _resetOtpTimer();

      return FacebookEmailVerificationResult(
        success: true,
        isEmailRegistered: userExistCheck.isEmailRegistered,
        email: email,
      );
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return FacebookEmailVerificationResult(
        success: false,
        isEmailRegistered: false,
        errorMessage:
            state.errorMessage.isNotEmpty ? state.errorMessage : "Failed to send verification code. Please try again.",
        email: email,
      );
    }
  }

  /// **Facebook Authentication Flow - Step 3: OTP Verification**
  ///
  /// Verifies the OTP code entered by the user during Facebook email verification process.
  /// Handles both existing user sign-in and new user registration scenarios.
  ///
  /// **Use Case**: Called when user enters OTP code on Facebook verification page
  ///
  /// **Parameters**:
  /// - `otp`: 6-digit OTP code entered by the user
  ///
  /// **Returns**:
  /// - `true` if OTP verification successful and user proceeds to next step
  /// - `false` if OTP is invalid, expired, or verification fails

  Future<bool> verifyFacebookOtpCode(String otp) async {
    try {
      emit(state.copyWith(errorMessage: ''));
      showLoading();

      final email = await sharedPreferencesService.getEmail();
      final countryCode = await sharedPreferencesService.getCountryCode();
      final isRegistered = await sharedPreferencesService.getBoolFromInstance(InstanceConstants.isEmailRegisteredKey);

      if (email == null || email.isEmpty || countryCode == null) {
        emit(state.copyWith(errorMessage: "Email or country code is not set"));
        return false;
      }

      final facebookAuthService = Modular.get<FacebookAuthService>();
      final success = await facebookAuthService.handleFacebookVerifyOtp(
          otp, email, countryCode, isRegistered!, (errorMessage) => emit(state.copyWith(errorMessage: errorMessage)));

      if (success) {
        _clearOtpTimer();

        if (isRegistered) {
          final facebookAuthData = await storage.read(key: InstanceConstants.facebookAuthDataKey);
          if (facebookAuthData != null) {
            final data = jsonDecode(facebookAuthData);
            final accessToken = data['accessToken'];

            if (accessToken != null) {
              return await authenticateExistingFacebookUser(accessToken);
            }
          }
        } else {
          final facebookAuthData = await storage.read(key: InstanceConstants.facebookAuthDataKey);
          if (facebookAuthData != null) {
            final data = jsonDecode(facebookAuthData);
            if (data['first_name'] != null && data['last_name'] != null) {
              await Future.wait([
                sharedPreferencesService.setToInstance(InstanceConstants.firstNameKey, data['first_name']),
                sharedPreferencesService.setToInstance(InstanceConstants.lastNameKey, data['last_name']),
                sharedPreferencesService.setToInstance(InstanceConstants.facebookIdKey, data['id']),
              ]);

              if (data['picture'] != null && data['picture']['data']['url'] != null) {
                await sharedPreferencesService.setToInstance(
                    InstanceConstants.profilePictureUrlKey, data['picture']['data']['url']);
              }
            }
          }
          emit(state.copyWith(route: '/survey/user'));
        }
      }

      return success;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return false;
    } finally {
      hideLoading();
    }
  }

  /// **Facebook Authentication Flow - Step 4: Existing User Login**
  ///
  /// Processes Facebook login for users who already have accounts in the system.
  /// This method handles the final authentication step after Facebook token validation.
  ///
  /// **Use Case**: Called when a user with an existing account completes Facebook sign-in
  ///
  /// **Parameters**:
  /// - `accessToken`: Valid Facebook access token obtained from Facebook SDK
  ///
  /// **Returns**:
  /// - `true` if login successful and user is authenticated
  /// - `false` if login fails due to token issues, network errors, or backend validation

  Future<bool> authenticateExistingFacebookUser(String accessToken) async {
    try {
      emit(state.copyWith(errorMessage: ''));
      showLoading();

      final facebookAuthService = Modular.get<FacebookAuthService>();
      if (!await facebookAuthService.validateFacebookTokenAndData(
          accessToken, (errorMessage) => emit(state.copyWith(errorMessage: errorMessage)))) {
        return false;
      }

      final facebookResponse = FacebookAuthResponse(
        id: '',
        accessToken: accessToken,
      );

      return await _performFacebookLogin(facebookResponse);
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return false;
    } finally {
      hideLoading();
    }
  }

  /// **Facebook Authentication Utility - Data Retrieval**
  ///
  /// Retrieves stored Facebook user data from secure storage for use in authentication flows.
  /// This is a utility method used by other Facebook authentication methods.
  ///
  /// **Use Case**: Internal utility for accessing cached Facebook user information
  ///
  /// **Parameters**: None
  ///
  /// **Returns**:
  /// - `FacebookAuthResponse` object containing user data if available
  /// - `null` if no data found or retrieval fails

  Future<FacebookAuthResponse?> retrieveStoredFacebookUserData() async {
    try {
      final facebookAuthService = Modular.get<FacebookAuthService>();
      final response = await facebookAuthService.getFacebookAuthResponseFromStorage();

      if (response == null) {
        emit(state.copyWith(errorMessage: 'Failed to retrieve Facebook data.'));
      }

      return response;
    } catch (e) {
      emit(state.copyWith(errorMessage: 'Failed to retrieve Facebook data.'));
      return null;
    }
  }

  Future<void> clearSocialLoginData() async {
    try {
      final googleAuthService = Modular.get<GoogleAuthService>();
      await googleAuthService.signOut();

      final facebookAuthService = Modular.get<FacebookAuthService>();
      await facebookAuthService.signOut();

      await sharedPreferencesService.deleteKey(InstanceConstants.facebookIdKey);
      await sharedPreferencesService.deleteKey(InstanceConstants.firstNameKey);
      await sharedPreferencesService.deleteKey(InstanceConstants.lastNameKey);
      await sharedPreferencesService.deleteKey(InstanceConstants.profilePictureUrlKey);

      emit(state.copyWith(
        oAuth2AccessToken: '',
        accessToken: '',
        refreshToken: '',
      ));
    } catch (e) {
      dev.log('Error clearing social login data: $e');
    }
  }

  Future<String?> _validateCountryCode() async {
    final countryCode = await sharedPreferencesService.getCountryCode();
    if (countryCode == null) {
      return "Country code is not selected";
    }
    return null;
  }

  Future<void> _storeEmailAndRegistrationStatus(String email, bool isEmailRegistered) async {
    await Future.wait([
      sharedPreferencesService.setEmail(email),
      sharedPreferencesService.setToInstance(InstanceConstants.isEmailRegisteredKey, isEmailRegistered),
    ]);
  }

  void _resetOtpTimer() {
    final otpTimerCubit = Modular.get<OtpTimerCubit>();
    otpTimerCubit.resetTimerForInitialOtp();
  }

  void _clearOtpTimer() {
    try {
      final otpTimerCubit = Modular.get<OtpTimerCubit>();
      otpTimerCubit.dispose();
    } catch (e) {
      dev.log('Error clearing OTP timer: $e');
    }
  }

  /// **Facebook Token Validation**
  ///
  /// Validates Facebook authentication response and token to ensure they are valid
  /// before proceeding with authentication flow.
  ///
  /// **Parameters**:
  /// - `facebookResponse`: Facebook authentication response to validate
  ///
  /// **Returns**:
  /// - `true` if token is valid and can be used for authentication
  /// - `false` if token is invalid, limited, or missing
  Future<bool> _validateFacebookToken(FacebookAuthResponse facebookResponse) async {
    try {
      if (facebookResponse.accessToken == null || facebookResponse.accessToken!.isEmpty) {
        return false;
      }

      // Check for limited login token which is not supported by backend
      final tokenTypeData = await FacebookAuthService.storage.read(key: 'facebook_token_type');

      if (tokenTypeData == 'limited') {
        emit(state.copyWith(
            errorMessage:
                "Your device privacy settings are preventing Facebook login. Please adjust your privacy settings or use another login method."));
        return false;
      }

      return true;
    } catch (e) {
      dev.log('Error validating Facebook token: $e');
      return false;
    }
  }

  Future<void> sendOtpResetPassword(String email) async {
    try {
      final countryCode = await commonCubit.sharedPreferencesService.getCountryCode();
      if (countryCode == null) {
        emit(state.copyWith(errorMessage: "Country code is not selected"));
        return;
      }
      await _accountRepository.sendOtpResetPassword(email, countryCode);
    } catch (e) {
      handleError(e, (message) => {emit(state.copyWith(errorMessage: message))});
    }
  }

  Future<bool> verifyOtpResetPassword(String email, String otp) async {
    try {
      final countryCode = await commonCubit.sharedPreferencesService.getCountryCode();
      if (countryCode == null) {
        emit(state.copyWith(errorMessage: "Country code is not selected"));
        return false;
      }
      final result = await _accountRepository.verifyOtpResetPassword(email, countryCode, otp);
      final authData = AuthTokenInfo.fromJson(result);
      emit(state.copyWith(authTokenInfo: authData));
      return true;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return false;
    }
  }

  Future<void> sendPaymentOtp() async {
    try {
      await _authenticationRepository.sendPaymentOtp();
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }
}
