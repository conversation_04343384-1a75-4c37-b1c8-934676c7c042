import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:koc_app/src/modules/account/data/repository/account_repository.dart';
import 'package:koc_app/src/modules/authentication/cubit/authentication_cubit.dart';
import 'package:koc_app/src/modules/authentication/data/repository/authentication_repository.dart';
import 'package:koc_app/src/modules/authentication/guards/facebook_auth_guard.dart';
import 'package:koc_app/src/modules/authentication/presentation/page/facebook_email_verification_page.dart';
import 'package:koc_app/src/modules/authentication/presentation/page/facebook_verification_code_page.dart';
import 'package:koc_app/src/modules/authentication/presentation/page/sign_in_by_otp_page.dart';
import 'package:koc_app/src/modules/authentication/presentation/page/sign_in_by_password_page.dart';
import 'package:koc_app/src/modules/authentication/presentation/page/sign_in_or_sign_up_page.dart';
import 'package:koc_app/src/modules/authentication/presentation/page/sign_up_page.dart';
import 'package:koc_app/src/modules/shared/cubit/otp_timer_cubit.dart';
import 'package:koc_app/src/modules/shared/shared_module.dart';
import 'package:koc_app/src/shared/config/env_config.dart';
import 'package:koc_app/src/shared/services/facebook_auth_service.dart';
import 'package:koc_app/src/shared/services/google_auth_service.dart';
import 'package:koc_app/src/shared/services/shared_preferences_service.dart';

class AuthenticationModule extends Module {
  @override
  List<Module> get imports => [
        SharedModule(),
        SharedAuthenticationModule(),
      ];

  @override
  void binds(Injector i) {
    i.addLazySingleton(AuthenticationRepository.new);
    i.addLazySingleton(AccountRepository.new);
    i.addLazySingleton(AuthenticationCubit.new);
    i.addLazySingleton(FacebookAuthService.new);
    i.addLazySingleton(OtpTimerCubit.new);
  }

  @override
  void routes(RouteManager r) {
    r.child('/', child: (_) => const SignInOrSignUpPage());
    r.child('/sign-up/',
        child: (_) => MultiBlocProvider(
              providers: [BlocProvider.value(value: Modular.get<OtpTimerCubit>())],
              child: const SignUpPage(),
            ));
    r.child('/sign-in-by-otp/',
        child: (_) => MultiBlocProvider(
              providers: [BlocProvider.value(value: Modular.get<OtpTimerCubit>())],
              child: const SignInByOtpPage(),
            ));
    r.child('/sign-in-by-password/', child: (_) => const SignInByPasswordPage());
    r.child('/facebook-email-verification/',
        child: (_) => const FacebookEmailVerificationPage(), guards: [FacebookAuthGuard()]);
    r.child('/facebook-verification-code/',
        child: (_) => MultiBlocProvider(
              providers: [BlocProvider.value(value: Modular.get<OtpTimerCubit>())],
              child: FacebookVerificationCodePage(
                email: r.args.data['email'] ?? '',
                isRegistered: r.args.data['isRegistered'] ?? false,
              ),
            ),
        guards: [FacebookAuthGuard()]);
  }
}

class SharedAuthenticationModule extends Module {
  @override
  void binds(Injector i) {
    i.addLazySingleton(() => GoogleSignIn(
          scopes: ['email', 'profile'],
          clientId: EnvConfig.isAndroid ? null : EnvConfig.googleClientId,
        ));
    i.addLazySingleton(GoogleAuthService.new);
    i.addLazySingleton<FacebookAuthService>((i) => FacebookAuthService(
          i.get<AuthenticationRepository>(),
          i.get<SharedPreferencesService>(),
        ));
  }
}
