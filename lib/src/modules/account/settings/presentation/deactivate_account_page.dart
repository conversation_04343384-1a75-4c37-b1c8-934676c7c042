import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/account/cubit/account_cubit.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';
import 'package:koc_app/src/shared/mixin/common_mixin.dart';
import 'package:koc_app/src/shared/extensions.dart';

class DeactivateAccountPage extends StatefulWidget {
  const DeactivateAccountPage({super.key});

  @override
  State<DeactivateAccountPage> createState() => _DeactivateAccountPageState();
}

class _DeactivateAccountPageState extends BasePageState<DeactivateAccountPage, AccountCubit> with CommonMixin {
  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: const CommonAppBar(title: Text('Deactivate')),
      body: _buildBody(),
      bottomSheet: Container(
        height: 76.r,
        color: Colors.white,
        alignment: Alignment.center,
        child: GestureDetector(
          onTap: () async {
            final success = await cubit.deactivateAccountAndLogout();
            if (!success && context.mounted) {
              context.showSnackBar('Deactivation failed. Please try again.');
            }
          },
          child: Text(
            'Deactivate',
            style: Theme.of(context)
                .textTheme
                .labelLarge!
                .copyWith(fontWeight: FontWeight.w500, color: const Color(0xFFEF6507)),
          ),
        ),
      ),
    );
  }

  Widget _buildBody() {
    return Padding(
      padding: EdgeInsets.all(16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        spacing: 16.r,
        children: [
          Text(
            'Deactivate your account?',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          Text(
            'Your account and all its data will be permanently removed and cannot be recovered.',
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
