import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/account/cubit/account_cubit.dart';
import 'package:koc_app/src/modules/account/data/model/bank_info.dart';
import 'package:koc_app/src/modules/account/presentation/widget/confirmation_buttons.dart';
import 'package:koc_app/src/modules/account/settings/cubit/account_settings_cubit.dart';
import 'package:koc_app/src/modules/account/settings/cubit/bank_list_cubit.dart';
import 'package:koc_app/src/modules/account/settings/cubit/edit_payment_cubit.dart';
import 'package:koc_app/src/modules/account/settings/cubit/edit_payment_state.dart';
import 'package:koc_app/src/modules/account/settings/data/model/account_payment.dart';
import 'package:koc_app/src/modules/account/settings/data/model/currency_info.dart';
import 'package:koc_app/src/shared/constants/color_constants.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/modules/shared/model/otp_endpoint.dart';

class EditPaymentPage extends StatefulWidget {
  const EditPaymentPage({super.key});

  @override
  State<EditPaymentPage> createState() => _EditPaymentPageState();
}

class _EditPaymentPageState extends BasePageState<EditPaymentPage, AccountSettingsCubit> {
  final TextEditingController _accountNameController = TextEditingController();
  final TextEditingController _accountNumberController = TextEditingController();
  final TextEditingController _codeController = TextEditingController();
  final TextEditingController _branchNameController = TextEditingController();
  final TextEditingController _branchAddressController = TextEditingController();
  final TextEditingController _customBankNameController = TextEditingController();
  final TextEditingController _branchZipCodeController = TextEditingController();
  late final AccountCubit accountCubit = Modular.get<AccountCubit>();
  late final EditPaymentCubit editPaymentCubit = Modular.get<EditPaymentCubit>();
  bool _initialized = false;
  Map<TextEditingController, bool> _showErrors = {};

  @override
  void initState() {
    _showErrors = {
      _accountNameController: false,
      _accountNumberController: false,
      _branchNameController: false,
      _branchAddressController: false,
      _branchZipCodeController: false,
      _customBankNameController: false,
    };
    initialize();
    super.initState();
  }

  Future<void> initialize() async {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (mounted) {
        await _initializeFormAsync();
      }
    });
  }

  Future<void> _initializeFormAsync() async {
    final bool fetched = await _fetchBankDetails();
    if (!mounted || !fetched) return;

    final payment = cubit.state.payment;
    _accountNameController.text = payment.accountName;
    _accountNumberController.text = payment.accountNumber;
    _codeController.text = payment.code;
    _branchNameController.text = payment.branchName;
    _branchAddressController.text = payment.branchAddress;
    _branchZipCodeController.text = payment.zipCode;

    await ReadContext(context).read<BankListCubit>().fetchBanks();
    if (!mounted) return;
    final banks = ReadContext(context).read<BankListCubit>().state;
    final bankExists = banks.any((b) => b.name == payment.bankName);
    final editPaymentCubit = ReadContext(context).read<EditPaymentCubit>();

    if (!bankExists && payment.bankName.isNotEmpty) {
      _customBankNameController.text = payment.bankName;
      ReadContext(context).read<EditPaymentCubit>()
        ..updateSelectedBank(const BankInfo(name: 'Others', value: -1))
        ..updateCustomBankName(payment.bankName);
    } else if (bankExists) {
      final selectedBank = banks.firstWhere((b) => b.name == payment.bankName);
      editPaymentCubit.updateSelectedBank(selectedBank);
    }
    editPaymentCubit
      ..updateSelectedBankAccountType(payment.accountType)
      ..updateSelectedCurrency(payment.currency.code)
      ..updateSelectedBranchName(payment.branchName)
      ..updateHasChanges(false);
  }

  void _initializeForm() {
    _initializeFormAsync();
  }

  Future<bool> _fetchBankDetails() async {
    if (accountCubit.state.paymentOtpToken.isEmpty) {
      if (mounted) {
        Modular.to.pushNamedAndRemoveUntil(
            '/account/settings/account-verification', (route) => route.settings.name == '/account/settings/',
            arguments: OtpEndpoint.showPayment);
      }
      return false;
    }

    bool hasError = false;
    await doLoadingAction(() async {
      try {
        await cubit.getBankAccount();
      } catch (e) {
        hasError = true;
        if (mounted) {
          cubit.clearError();
          Modular.to.pushNamedAndRemoveUntil(
              '/account/settings/account-verification', (route) => route.settings.name == '/account/settings/',
              arguments: OtpEndpoint.showPayment);
        }
      }
    });
    return !hasError;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_initialized) {
      _initializeForm();
      _initialized = true;
    }
  }

  @override
  void dispose() {
    _accountNameController.dispose();
    _accountNumberController.dispose();
    _codeController.dispose();
    _branchNameController.dispose();
    _branchAddressController.dispose();
    _customBankNameController.dispose();
    _branchZipCodeController.dispose();
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: const CommonAppBar(title: Text('Payment')),
      body: Column(
        children: [
          Expanded(
            child: _buildBody(),
          ),
          KeyboardVisibilityBuilder(
            builder: (context, isKeyboardVisible) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (isKeyboardVisible) Divider(height: 1.r),
                  Container(
                    color: Colors.white,
                    padding: EdgeInsets.all(16.r),
                    child: BlocBuilder<EditPaymentCubit, EditPaymentState>(
                      builder: (context, state) {
                        return ConfirmationButtons(
                          btnName: 'Change',
                          onTap: () async {
                            final payment = _createAccountPayment();
                            commonCubit.showLoading();
                            final success = await cubit.updateBankAccount(payment);

                            if (!mounted) return;
                            commonCubit.hideLoading();

                            if (success) {
                              Modular.to.pop(true);
                            } else if (cubit.state.errorMessage.isNotEmpty) {
                              context.showSnackBar(cubit.state.errorMessage);
                              cubit.clearError();
                            }
                          },
                          onCancel: () {
                            showWarningDialog();
                          },
                          showCancelButton: true,
                          isValid: state.hasChanges && state.isValid,
                        );
                      },
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  void showWarningDialog() async {
    final result = await showDialog<bool?>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        title: Text(
          'Do you want to leave?',
          style: context.textBodyMedium(),
        ),
        content: Text('Data you have entered may not be saved', style: context.textLabelLarge()),
        actions: <Widget>[
          TextButton(
            child: Text(
              'Cancel',
              style: Theme.of(context).textTheme.labelLarge!.copyWith(color: const Color(0xFFEF6507)),
            ),
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
          ElevatedButton(
            child: Text(
              'Leave',
              style: Theme.of(context).textTheme.labelLarge!.copyWith(color: Colors.white),
            ),
            onPressed: () {
              Navigator.of(context).pop(true);
            },
          ),
        ],
      ),
    );

    if (result == true) {
      Modular.to.pop();
    }
  }

  Widget _buildBody() {
    EditPaymentCubit editPaymentCubit = ReadContext(context).read<EditPaymentCubit>();
    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          spacing: 16.r,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Fill in your bank account details to ensure prompt payments.',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            BlocBuilder<EditPaymentCubit, EditPaymentState>(
              builder: (context, state) {
                return Column(
                  children: [
                    if (state.selectedBankId == -1)
                      // Show both fields in same row when "Others" is selected
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            flex: 1,
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                _buildDropdownField('Bank name', state.selectedBank, () {
                                  final banks = ReadContext(context).read<BankListCubit>().state;
                                  _showSelectionDialog('Bank', banks.map((b) => b.name).toList(), editPaymentCubit);
                                }),
                                SizedBox(height: _showErrors[_customBankNameController] == true ? 24.r : 0),
                              ],
                            ),
                          ),
                          SizedBox(width: 10.r),
                          Expanded(
                            flex: 1,
                            child: _buildTextField(
                              _customBankNameController,
                              'Bank name',
                              1,
                              isRequired: true,
                            ),
                          ),
                        ],
                      )
                    else
                      _buildDropdownField('Bank name', state.selectedBank, () {
                        final banks = ReadContext(context).read<BankListCubit>().state;
                        _showSelectionDialog('Bank', banks.map((b) => b.name).toList(), editPaymentCubit);
                      }),
                  ],
                );
              },
            ),
            _buildTextField(_accountNameController, 'Account name', 1, isRequired: true),
            _buildTextField(_accountNumberController, 'Account number', 1, isRequired: true),
            _buildTextField(_codeController, 'SWIFT/BIC Code (Optional)', 1),
            Row(
              children: [
                Expanded(
                  child: BlocBuilder<EditPaymentCubit, EditPaymentState>(builder: (context, state) {
                    return _buildDropdownField('Account type', state.selectedBankAccountType.name, () {
                      _showSelectionDialog(
                          'Account type',
                          BankAccountType.values.map((type) => type.toString().split('.').last).toList(),
                          editPaymentCubit);
                    });
                  }),
                ),
                SizedBox(width: 10.r),
                BlocBuilder<EditPaymentCubit, EditPaymentState>(builder: (context, state) {
                  return Expanded(
                    child: _buildDropdownField('Currency', state.selectedCurrency, () {
                      _showSelectionDialog('Currency', ['USD', 'VND', 'THB', 'JPY'], editPaymentCubit);
                    }),
                  );
                }),
              ],
            ),
            _buildTextField(_branchNameController, 'Branch name', 1, isRequired: true),
            _buildTextField(_branchAddressController, 'Branch Address', 3,
                subtitle: 'Street, province, city, etc.', isRequired: true),
            _buildTextField(_branchZipCodeController, 'Branch Zip Code', 1, isRequired: true),
            KeyboardVisibilityBuilder(builder: (_, isKeyboardVisible) {
              return isKeyboardVisible ? SizedBox(height: 50.r) : const SizedBox.shrink();
            }),
          ],
        ),
      ),
    );
  }

  AccountPayment _createAccountPayment() {
    EditPaymentState editPaymentState = ReadContext(context).read<EditPaymentCubit>().state;
    return cubit.state.payment.copyWith(
        accountName: _accountNameController.text,
        bankId: editPaymentState.selectedBankId,
        bankName:
            editPaymentState.selectedBankId == -1 ? _customBankNameController.text : editPaymentState.selectedBank,
        accountNumber: _accountNumberController.text,
        code: _codeController.text,
        accountType: editPaymentState.selectedBankAccountType,
        currency: CurrencyInfo(code: editPaymentState.selectedCurrency),
        branchName: _branchNameController.text,
        branchAddress: _branchAddressController.text,
        zipCode: _branchZipCodeController.text);
  }

  Widget _buildTextField(TextEditingController controller, String hint, int maxLines,
      {String? subtitle, bool isRequired = false}) {
    final hasError = _showErrors[controller] == true;
    final borderColor = hasError ? Colors.red : const Color(0xFFE7E7E7);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: double.infinity,
          height: maxLines > 1 ? null : 50.r,
          child: TextField(
            onChanged: (value) {
              setState(() {
                _showErrors[controller] = isRequired && value.trim().isEmpty;
              });
              final editPaymentCubit = ReadContext(context).read<EditPaymentCubit>();
              editPaymentCubit
                ..updateHasChanges(cubit.state.payment != _createAccountPayment())
                ..updateIsValid(_isFormValid());
            },
            controller: controller,
            maxLines: maxLines,
            style: Theme.of(context).textTheme.bodySmall,
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: context.textBodySmall(color: ColorConstants.hintColor),
              border: maxLines > 1
                  ? OutlineInputBorder(
                      borderRadius: BorderRadius.circular(20.r),
                      borderSide: BorderSide(
                        color: borderColor,
                      ))
                  : OutlineInputBorder(
                      borderRadius: BorderRadius.circular(9999),
                      borderSide: BorderSide(
                        color: borderColor,
                      )),
              enabledBorder: maxLines > 1
                  ? OutlineInputBorder(
                      borderRadius: BorderRadius.circular(20.r),
                      borderSide: BorderSide(
                        color: borderColor,
                      ))
                  : OutlineInputBorder(
                      borderRadius: BorderRadius.circular(9999),
                      borderSide: BorderSide(
                        color: borderColor,
                      )),
              focusedBorder: maxLines > 1
                  ? OutlineInputBorder(
                      borderRadius: BorderRadius.circular(20.r),
                      borderSide: BorderSide(
                        color: borderColor,
                      ))
                  : OutlineInputBorder(
                      borderRadius: BorderRadius.circular(9999),
                      borderSide: BorderSide(
                        color: borderColor,
                      )),
            ),
          ),
        ),
        if (subtitle != null || hasError)
          Padding(
            padding: EdgeInsets.only(top: 4.r, left: 16.r),
            child: Text(
              hasError ? 'Enter ${hint.toLowerCase()}' : subtitle ?? '',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: hasError ? Colors.red : const Color(0xFF666666),
                  ),
            ),
          ),
      ],
    );
  }

  Widget _buildDropdownField(String hint, String? value, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.only(left: 10.r),
        height: 50.r,
        decoration: BoxDecoration(
          border: Border.all(color: const Color(0xFFE7E7E7)),
          borderRadius: BorderRadius.circular(9999),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text((value?.isEmpty ?? true) ? hint : value!,
                style: context.textBodySmall(color: ColorConstants.hintColor)),
            Icon(
              Icons.arrow_drop_down,
              size: 25.r,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showSelectionDialog(String title, List<String> items, EditPaymentCubit editPaymentCubit) async {
    showModalBottomSheet<String>(
      useSafeArea: true,
      isScrollControlled: true,
      context: context,
      backgroundColor: Colors.white,
      builder: (context) => Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          spacing: 20.r,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              spacing: 16.r,
              children: [
                GestureDetector(
                  onTap: () {
                    Modular.to.pop();
                  },
                  child: Icon(
                    Icons.close,
                    size: 20.r,
                  ),
                ),
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontWeight: FontWeight.w500),
                )
              ],
            ),
            Flexible(
              child: SingleChildScrollView(
                child: Container(
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12.r),
                      border: Border.all(color: const Color(0xFFE7E7E7))),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12.r),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: items.expand((item) {
                        return [
                          BlocBuilder<EditPaymentCubit, EditPaymentState>(
                              bloc: editPaymentCubit,
                              builder: (context, state) {
                                return GestureDetector(
                                  onTap: () {
                                    updatePaymentData(title, item);
                                  },
                                  child: Container(
                                    color: _getColor(title, item),
                                    width: double.infinity,
                                    padding: EdgeInsets.symmetric(horizontal: 12.r, vertical: 8.r),
                                    alignment: Alignment.centerLeft,
                                    child: Text(
                                      item,
                                      style: Theme.of(context).textTheme.bodySmall,
                                    ),
                                  ),
                                );
                              }),
                          Divider(
                            color: const Color(0xFFE7E7E7),
                            height: 1.r,
                          ), // 각 아이템 뒤에 Divider 추가
                        ];
                      }).toList()
                        ..removeLast(),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getColor(String title, String item) {
    EditPaymentCubit editPaymentCubit = ReadContext(context).read<EditPaymentCubit>();
    if (title == 'Bank' && item == editPaymentCubit.state.selectedBank) {
      return ColorConstants.selectedColor;
    } else if (title == 'Account type' && item == editPaymentCubit.state.selectedBankAccountType.name) {
      return ColorConstants.selectedColor;
    } else if (title == 'Currency' && item == editPaymentCubit.state.selectedCurrency) {
      return ColorConstants.selectedColor;
    } else if (title == 'Branch name' && item == editPaymentCubit.state.selectedBranchName) {
      return ColorConstants.selectedColor;
    }
    return Colors.white;
  }

  void updatePaymentData(String title, String selectedItem) {
    EditPaymentCubit editPaymentCubit = ReadContext(context).read<EditPaymentCubit>();
    if (title == 'Bank') {
      final banks = ReadContext(context).read<BankListCubit>().state;
      final selectedBank = banks.firstWhere((b) => b.name == selectedItem);
      editPaymentCubit.updateSelectedBank(selectedBank);
    } else if (title == 'Account type') {
      editPaymentCubit.updateSelectedBankAccountType(BankAccountType.fromString(selectedItem)!);
    } else if (title == 'Currency') {
      editPaymentCubit.updateSelectedCurrency(selectedItem);
    } else if (title == 'Branch name') {
      editPaymentCubit.updateSelectedBranchName(selectedItem);
    }
    editPaymentCubit
      ..updateHasChanges(cubit.state.payment != _createAccountPayment())
      ..updateIsValid(_isFormValid());
    Modular.to.pop();
  }

  bool _isFormValid() {
    if (_accountNameController.text.isEmpty ||
        _accountNumberController.text.isEmpty ||
        _branchNameController.text.isEmpty ||
        _branchAddressController.text.isEmpty ||
        _branchZipCodeController.text.isEmpty) {
      return false;
    }
    EditPaymentState state = ReadContext(context).read<EditPaymentCubit>().state;
    if (state.selectedBankId == 0) {
      return false;
    }
    if (state.selectedBankId == -1 && _customBankNameController.text.isEmpty) {
      return false;
    }
    if (state.selectedCurrency.isEmpty) {
      return false;
    }
    return true;
  }
}
