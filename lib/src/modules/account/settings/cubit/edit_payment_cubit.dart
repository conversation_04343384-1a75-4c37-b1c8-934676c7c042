import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koc_app/src/modules/account/data/model/bank_info.dart';
import 'package:koc_app/src/modules/account/settings/cubit/edit_payment_state.dart';
import 'package:koc_app/src/modules/account/settings/data/model/account_payment.dart';

class EditPaymentCubit extends Cubit<EditPaymentState> {
  EditPaymentCubit() : super(EditPaymentState());

  void initialize(String selectedBank, BankAccountType selectedBankAccountType, String selectedCurrency,
      String selectedBranchName) {
    emit(state.copyWith(
        selectedBank: selectedBank,
        selectedBankAccountType: selectedBankAccountType,
        selectedBranchName: selectedBranchName,
        selectedCurrency: selectedCurrency));
  }

  void updateSelectedBank(BankInfo selectedBank) {
    emit(state.copyWith(selectedBank: selectedBank.name, selectedBankId: selectedBank.value));
  }

  void updateSelectedBankAccountType(BankAccountType selectedBankAccountType) {
    emit(state.copyWith(selectedBankAccountType: selectedBankAccountType));
  }

  void updateSelectedBranchName(String selectedBranchName) {
    emit(state.copyWith(selectedBranchName: selectedBranchName));
  }

  void updateSelectedCurrency(String selectedCurrency) {
    emit(state.copyWith(selectedCurrency: selectedCurrency));
  }

  void updateHasChanges(bool value) {
    emit(state.copyWith(hasChanges: value));
  }

  updateCustomBankName(String name) {
    emit(state.copyWith(customBankName: name));
  }

  void updateIsValid(bool value) {
    emit(state.copyWith(isValid: value));
  }
}
