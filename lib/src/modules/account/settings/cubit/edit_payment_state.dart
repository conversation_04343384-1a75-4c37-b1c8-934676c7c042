import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/modules/account/settings/data/model/account_payment.dart';

part 'edit_payment_state.freezed.dart';
part 'edit_payment_state.g.dart';

@freezed
class EditPaymentState with _$EditPaymentState {
  factory EditPaymentState(
      {@Default(0) int selectedBankId,
      @Default('') String selectedBank,
      @Default(BankAccountType.CURRENT) BankAccountType selectedBankAccountType,
      @Default('') String selectedCurrency,
      @Default('') String selectedBranchName,
      @Default(false) bool hasChanges,
      @Default('') String customBankName,
      @Default(false) bool isValid}) = _EditPaymentState;

  factory EditPaymentState.fromJson(Map<String, Object?> json) => _$EditPaymentStateFromJson(json);
}
