import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/account/cubit/account_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/account_state.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';

class AccountPersonalizedPage extends StatefulWidget {
  const AccountPersonalizedPage({super.key});

  @override
  State<AccountPersonalizedPage> createState() => _AccountPersonalizedPageState();
}

class _AccountPersonalizedPageState extends BasePageState<AccountPersonalizedPage, AccountCubit> {
  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: const CommonAppBar(
        title: Text('Personalized'),
        showBottomDivider: false,
      ),
      body: _buildBody(),
    );
  }

  Widget _buildPersonalizedCard(AccountState state) {
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r), border: Border.all(color: Colors.grey[200]!, width: 1.r)),
      child: Column(
        children: [
          _buildPerionalizedTile('Traffic sources', '/account/traffic-sources'),
          Divider(
            color: Colors.grey[200],
            height: 1.r,
          ),
          _buildPerionalizedTile('Interested fields', '/account/interested-fields'),
        ],
      ),
    );
  }

  Widget _buildBody() {
    return Padding(
      padding: EdgeInsets.all(16.r),
      child: BlocBuilder<AccountCubit, AccountState>(
          bloc: cubit,
          builder: (_, state) {
            return Column(
              spacing: 16.r,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'This helps ACCESSTRADE identify patterns and preferences, enabling the services to make personalized recommendations.',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                _buildPersonalizedCard(state),
              ],
            );
          }),
    );
  }

  Widget _buildPerionalizedTile(String title, String route) {
    return InkWell(
      onTap: () {
        Modular.to.pushNamed(route);
      },
      borderRadius: BorderRadius.circular(12.r),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 24.r, horizontal: 16.r),
        child: Row(
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.w500),
            ),
          ],
        ),
      ),
    );
  }
}
