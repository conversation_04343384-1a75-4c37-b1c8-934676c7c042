import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/account/cubit/account_avatar_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/account_avatar_state.dart';
import 'package:koc_app/src/modules/authentication/data/repository/authentication_repository.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../../shared/mixin/common_mixin.dart';

class AccountPage extends StatefulWidget {
  const AccountPage({super.key});

  @override
  State<AccountPage> createState() => _AccountPageState();
}

class _AccountPageState extends BasePageState<AccountPage, AccountAvatarCubit> with CommonMixin {
  bool _isNotificationEnabled = true;

  @override
  void initState() {
    doLoadingAction(() async {
      await cubit.findAvatar();
    });
    super.initState();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: _buildTitle(),
        showNotificationAction: true,
        showBottomDivider: false,
      ),
      body: _buildBody(),
    );
  }

  Widget _buildTitle() {
    return BlocBuilder<AccountAvatarCubit, AccountAvatarState>(
      bloc: cubit,
      builder: (context, state) {
        if (state != const AccountAvatarState()) {
          return Row(
            spacing: 8.r,
            children: [
              CircleAvatar(
                  radius: 16.r,
                  backgroundImage: cubit.state.avatar.isNotEmpty ? NetworkImage(cubit.state.avatar) : null,
                  backgroundColor: Colors.amber,
                  child: cubit.state.avatar.isEmpty && cubit.state.name.isNotEmpty
                      ? Text(cubit.state.name.substring(0, 1))
                      : null),
              Text(
                state.name,
                style: Theme.of(context).textTheme.bodySmall,
              )
            ],
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildTile(IconData iconData, String title, {String? description, Widget? leading, VoidCallback? onTap}) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12.r),
      child: Padding(
        padding: EdgeInsets.all(18.r),
        child: Row(
          spacing: 16.r,
          children: [
            Icon(
              iconData,
              size: 20.r,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(title, style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.w500)),
                  if (description != null)
                    Text(
                      description,
                      style: Theme.of(context).textTheme.labelLarge!.copyWith(color: const Color(0xFF767676)),
                      overflow: TextOverflow.ellipsis,
                    ),
                ],
              ),
            ),
            if (leading != null) Expanded(child: Align(alignment: Alignment.centerRight, child: leading))
          ],
        ),
      ),
    );
  }

  Widget _buildCardTemplate(Widget data) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(16.r),
      child: Container(
        color: Colors.white,
        child: data,
      ),
    );
  }

  Widget _buildAccountCard() {
    return _buildCardTemplate(
      Column(
        children: [
          _buildTile(Symbols.person_play_sharp, 'Personalized', onTap: () {
            Modular.to.pushNamed('/account/personalized');
          }, description: 'Traffic sources, interested fields'),
          Divider(
            color: Colors.grey[200],
            height: 1.r,
          ),
          _buildTile(Symbols.manage_accounts_sharp, 'Account settings', onTap: () {
            Modular.to.pushNamed('/account/settings');
          }, description: 'Profile picture, name, email, phone number, payment...'),
        ],
      ),
    );
  }

  Widget _buildLogoutCard() {
    return _buildCardTemplate(Column(
      children: [
        _buildTile(Icons.logout_outlined, 'Logout', onTap: () async {
          showConfirmationDialog(
              context,
              Text(
                'Logging out',
                style: Theme.of(context)
                    .textTheme
                    .bodyLarge!
                    .copyWith(color: const Color(0xFF464646), fontSize: 20, fontWeight: FontWeight.w400),
              ),
              Text('Are you sure want to logout?',
                  style: Theme.of(context)
                      .textTheme
                      .bodySmall!
                      .copyWith(color: const Color(0xFF464646), fontSize: 14, fontWeight: FontWeight.w500)),
              'Logout', () async {
            await Modular.get<AuthenticationRepository>().logOut();
            Modular.to.navigate('/');
          }, true);
        }),
      ],
    ));
  }

  Widget _buildNotificationCard() {
    return _buildCardTemplate(
      _buildTile(
        Icons.notifications_none,
        'Notification',
        leading: Transform.scale(
          scale: 0.7.r,
          alignment: Alignment.centerRight,
          child: Switch(
              activeTrackColor: Colors.amber,
              onChanged: (value) {
                setState(() {
                  _isNotificationEnabled = value;
                });
              },
              value: _isNotificationEnabled),
        ),
        description: 'Campaigns, Conversions',
      ),
    );
  }

  Widget _buildBody() {
    return Container(
      color: Colors.grey[200],
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          spacing: 16.r,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildAccountCard(),
            _buildNotificationCard(),
            _buildLogoutCard(),
          ],
        ),
      ),
    );
  }
}
