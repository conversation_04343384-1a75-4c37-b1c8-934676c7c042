import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:koc_app/src/modules/account/cubit/account_state.dart';
import 'package:koc_app/src/modules/account/data/model/account.dart';
import 'package:koc_app/src/modules/account/settings/data/model/account_payment.dart';
import 'package:koc_app/src/modules/authentication/data/models/otp.dart';
import 'package:koc_app/src/modules/survey/data/models/survey_info.dart';
import 'package:share_plus/share_plus.dart';

import '../../../../shared/services/api_service.dart';
import '../model/update_password_request.dart';

class AccountRepository {
  final ApiService apiService;
  static final String? clientId = dotenv.env['CLIENT_ID'];

  AccountRepository(this.apiService);

  AccountState account = AccountState(
    userId: 123,
    lastPasswordChangedOn: DateTime.now(),
    trafficSources: [],
    interestedFields: [],
    language: 'en',
    isNotificationEnabled: true,
    profilePictureUrl: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSvzybSjmoKrtjOozCvEyCmYHLtTJyX_0LSCw&s',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    // phoneNumber: '**********',
    address: '123 Example St, Example City',
    // lastPasswordChangedOn: DateTime.now().subtract(Duration(days: 3)),
    payment: const AccountPayment(),
  );

  Future<AccountData> getAccount() async {
    final result = await apiService.getData('/v3/publishers/me/account');
    return AccountData.fromJson(result as Map<String, dynamic>);
  }

  Future<void> updateAccount(AccountState accountState) async {
    account = accountState;
  }

  Future<bool> verifyPhoneOtp(String phone, String otp) async {
    await Future.delayed(const Duration(seconds: 1));
    account = account.copyWith(phoneVerifiedOn: DateTime.now());
    return true;
  }

  Future<bool> verifyCurrentPassword(String password) async {
    final result = await apiService.postData('/v3/users/me/password/validate', {'password': password});
    return result['valid'] as bool;
  }

  Future<dynamic> resetPassword(String password, String token) async {
    UpdatePasswordRequest request = UpdatePasswordRequest(password: password, clientId: clientId!);
    return await apiService.putDataWithTokenParam('/v1/me/password', request, token);
  }

  Future<dynamic> changePassword(String currentPassword, String newPassword) async {
    final data = {
      "currentPassword": currentPassword,
      "newPassword": newPassword,
    };
    return await apiService.putData('/v3/users/me/password', data);
  }
  Future<dynamic> sendOtpResetPassword(String email, String countryCode) async {
    SendCodeOtpRequest request = SendCodeOtpRequest(
      email: email, 
      countryCode: countryCode,
      otpType: OtpType.changePassword
    );
    return await apiService.postDataWithoutJwt('/v3/auth/send-otp', request);
  }

  Future<dynamic> verifyOtpResetPassword(String email, String countryCode, String otp) async {
    VerifySignInOtpRequest request = VerifySignInOtpRequest(
      email: email, 
      countryCode: countryCode, 
      otp: otp,
      otpType: OtpType.changePassword
    );
    return await apiService.postDataWithoutJwt('/v3/auth/verify-otp', request);
  }

  Future<dynamic> getSites() async {
    return await apiService.getData('/v3/publishers/me/sites');
  }

  Future<List<SocialInfo>> getTrafficSources() async {
    final result = await apiService.getData('/v3/publishers/me/sites');
    return (result as List).map((item) => SocialInfo.fromJson(item)).toList();
  }

  Future<dynamic> updateTrafficSources(SocialInfo trafficSource) async {
    return await apiService.putData('/v3/publishers/me/sites', trafficSource);
  }

  Future<dynamic> deleteTrafficSources(int id) async {
    return await apiService.deleteData('/v3/publishers/me/sites/$id', null);
  }

  Future<dynamic> getInterestedFields(int siteId) async {
    final result = await apiService.getData('/v3/publishers/me/sites/$siteId/categories');
    if (result is List) {
      final List<int> categoryIds = result.map((e) => e['id']).whereType<int>().cast<int>().toList();
      final interestedFields =
          categoryIds.map((id) => PassionateItem.fromJson(id)).whereType<PassionateItem>().toList();
      return interestedFields;
    }
    return [];
  }

  Future<dynamic> upsertInterestedFields(int siteId, List<int> categoryIds) async {
    final data = {
      "categoryIds": categoryIds,
    };
    return await apiService.putData('/v3/publishers/me/sites/$siteId/categories', data);
  }

  Future<String> findSsoKey() async {
    return await apiService.getPlainText('/v3/publishers/me/account/sso-key');
  }

  Future<dynamic> updateAvatar(XFile file) async {
    return await apiService.putFile('/v3/publishers/me/avatar', 'image', file);
  }

  Future<dynamic> updatePhoneNumber(String phoneNumber) async {
    final data = {
      "phoneNumber": phoneNumber,
    };
    return await apiService.patchData('/v3/publishers/me/account/phone', data);
  }

  Future<dynamic> updateAddress(String address) async {
    final data = {
      "address": address,
    };
    return await apiService.patchData('/v3/publishers/me/account/address', data);
  }

  Future<dynamic> updateName(String firstName, String lastName) async {
    final data = {
      "firstName": firstName,
      "lastName": lastName,
    };
    return await apiService.patchData('/v3/publishers/me/account/name', data);
  }

  Future<AccountAvatarResponse> findAvatar() async {
    final result = await apiService.getData('/v3/publishers/me/avatar');
    return AccountAvatarResponse.fromJson(result as Map<String, dynamic>);
  }

  Future<void> removeAvatar(String avatar) async {
    final data = {
      "avatar": avatar,
    };
    await apiService.deleteData('/v3/publishers/me/avatar', data);
  }

  Future<dynamic> verifyPaymentOtp(String otpValue) async {
    return await apiService.getData('/v3/publishers/me/otp/$otpValue');
  }

  Future<AccountPayment> getBankAccount(String otpToken) async {
    final response =
        await apiService.getDataWithHeaders('/v3/publishers/me/bankaccount', headers: {'X-OTP-TOKEN': otpToken});
    return AccountPayment.fromJson(response);
  }

  Future<void> updateBankAccount(AccountPayment payment, String otpToken) async {
    final Map<String, dynamic> body = {
      "bankId": payment.bankId,
      "bankName": payment.bankName,
      "branchName": payment.branchName,
      "accountNumber": payment.accountNumber,
      "accountHolderName": payment.accountName,
      "accountType": payment.accountType.name,
      "bankPassbookImageName": payment.bankPassbookImageName,
      "swiftBicCode": payment.code,
      "currency": {"code": payment.currency.code, "name": payment.currency.name},
      "address": payment.branchAddress,
      "zipCode": payment.zipCode,
      "ifsc": payment.ifsc
    };

    await apiService.putMultipartWithHeaders('/v3/publishers/me/bankaccount', body, headers: {'X-OTP-TOKEN': otpToken});
  }

  Future<void> deactivateAccount() async {
    await apiService.putData('/v3/users/me/deactivate', null);
  }
}
