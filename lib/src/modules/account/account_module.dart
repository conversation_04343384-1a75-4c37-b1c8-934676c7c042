import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/modules/account/cubit/account_avatar_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/account_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/interested_fields_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/site_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/traffic_sources_cubit.dart';
import 'package:koc_app/src/modules/account/data/repository/account_repository.dart';
import 'package:koc_app/src/modules/account/presentation/page/account_interested_fields_page.dart';
import 'package:koc_app/src/modules/account/presentation/page/account_personalized_page.dart';
import 'package:koc_app/src/modules/account/presentation/page/account_traffic_sources_page.dart';
import 'package:koc_app/src/modules/account/presentation/page/account_traffic_sources_update_page.dart';
import 'package:koc_app/src/modules/account/presentation/page/language_page.dart';
import 'package:koc_app/src/modules/account/settings/account_settings_module.dart';
import 'package:koc_app/src/modules/account/settings/cubit/account_settings_cubit.dart';
import 'package:koc_app/src/modules/account/settings/cubit/reset_password_cubit.dart';
import 'package:koc_app/src/modules/shared/shared_module.dart';
import 'package:koc_app/src/modules/survey/cubit/survey_tab_cubit.dart';
import 'package:koc_app/src/modules/survey/data/models/survey_info.dart';
import 'package:koc_app/src/modules/survey/survey_module.dart';

import '../shared/cubit/otp_timer_cubit.dart';

class AccountModule extends Module {
  @override
  List<Module> get imports => [SharedModule(), AccountSharedModule(), SurveySharedModule()];

  @override
  void binds(Injector i) {
    super.binds(i);
    i.addLazySingleton(InterestedFieldsCubit.new);
    i.add(TrafficSourcesCubit.new);
    i.addLazySingleton(OtpTimerCubit.new);
  }

  @override
  void routes(RouteManager r) {
    super.routes(r);
    r.module('/settings', module: AccountSettingsModule());
    r.child('/personalized', child: (i) => const AccountPersonalizedPage());
    r.child('/interested-fields',
        child: (i) => BlocProvider.value(
            value: Modular.get<InterestedFieldsCubit>(), child: const AccountInterestedFieldsPage()));
    r.child('/language', child: (i) => const LanguagePage());
    r.child('/traffic-sources',
        child: (i) => MultiBlocProvider(
              providers: [
                BlocProvider.value(value: Modular.get<SurveyTabCubit>()),
                BlocProvider.value(value: Modular.get<TrafficSourcesCubit>()),
              ],
              child: const AccountTrafficSourcesPage(),
            ));
    r.child('/traffic-sources/update',
        child: (i) => MultiBlocProvider(
                providers: [
                  BlocProvider.value(value: Modular.get<SurveyTabCubit>()),
                  BlocProvider.value(value: Modular.get<TrafficSourcesCubit>()),
                ],
                child: AccountTrafficSourcesUpdatePage(
                  r.args.data ?? const SocialInfo(),
                )));
  }
}

class AccountSharedModule extends Module {
  @override
  List<Module> get imports => [SharedModule()];

  @override
  void exportedBinds(Injector i) {
    super.exportedBinds(i);
    i.addLazySingleton(SiteCubit.new);
    i.addLazySingleton(AccountRepository.new);
    i.addLazySingleton(AccountCubit.new);
    i.addLazySingleton(AccountAvatarCubit.new);
    i.addLazySingleton(AccountSettingsCubit.new);
    i.add(ResetPasswordCubit.new);
  }
}
