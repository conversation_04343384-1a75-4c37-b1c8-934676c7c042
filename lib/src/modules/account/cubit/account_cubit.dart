import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/account_state.dart';
import 'package:koc_app/src/modules/account/data/model/account.dart';
import 'package:koc_app/src/modules/account/data/repository/account_repository.dart';
import 'package:koc_app/src/modules/authentication/data/models/otp.dart';
import 'package:koc_app/src/modules/survey/data/models/survey_info.dart';
import 'package:koc_app/src/shared/constants/instance_key.dart';
import 'package:koc_app/src/shared/services/api_service.dart';

import '../../../shared/utils/handle_error.dart';
import '../../authentication/data/models/auth_token_info.dart';
import '../../authentication/data/repository/authentication_repository.dart';

class AccountCubit extends BaseCubit<AccountState> {
  final AccountRepository _accountRepository;
  final AuthenticationRepository _authenticationRepository;
  static const storage = FlutterSecureStorage();

  AccountCubit(this._accountRepository, this._authenticationRepository) : super(AccountState());

  Future<void> getAccount() async {
    try {
      final result = await _accountRepository.getAccount();
      AccountData accountData = AccountData.fromJson(result as Map<String, dynamic>);
      final otpToken = await storage.read(key: InstanceConstants.paymentOtpTokenKey);
      emit(state.copyWith(
        firstName: accountData.firstName,
        lastName: accountData.lastName,
        email: accountData.email,
        phoneNumber: accountData.phoneNumber,
        address: accountData.address,
        profilePictureUrl: accountData.avatar,
        paymentOtpToken: otpToken ?? '',
      ));
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  Future<void> updateAccount(AccountState accountState) async {
    emit(accountState);
  }

  Future<void> updateNotifications(bool isNotificationEnabled) async {
    emit(state.copyWith(isNotificationEnabled: isNotificationEnabled));
  }

  Future<void> updateFacebookLogin(bool isFacebookLoginEnabled) async {
    emit(state.copyWith(isFacebookLoginEnabled: isFacebookLoginEnabled));
  }

  Future<void> updateBiometricsLogin(bool isBiometricsEnabled) async {
    emit(state.copyWith(isBiometricsEnabled: isBiometricsEnabled));
  }
  Future<void> sendOtpResetPassword(String email) async {
    try {
      final countryCode = await commonCubit.sharedPreferencesService.getCountryCode();
      if (countryCode == null) {
        emit(state.copyWith(errorMessage: "Country code is not selected"));
        return;
      }
      await _accountRepository.sendOtpResetPassword(email, countryCode);
    } catch (e) {
      handleError(e, (message) => {emit(state.copyWith(errorMessage: message))});
    }
  }

  Future<bool> verifyOtpResetPassword(String email, String otp) async {
    try {
      final countryCode = await commonCubit.sharedPreferencesService.getCountryCode();
      if (countryCode == null) {
        emit(state.copyWith(errorMessage: "Country code is not selected"));
        return false;
      }
      final result = await _accountRepository.verifyOtpResetPassword(email, countryCode, otp);
      final authData = AuthTokenInfo.fromJson(result);
      emit(state.copyWith(authTokenInfo: authData));
      return true;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return false;
    }
  }

   Future<void> sendOtpRegister(String email, String countryCode) async {
    SendCodeOtpRequest request = SendCodeOtpRequest(
      email: email,
      countryCode: countryCode,
      otpType: OtpType.registration
    );
    await _authenticationRepository.sendOtpRegister(request);
  }

  Future<bool> verifyCurrentPassword(String password) async {
    return await _accountRepository.verifyCurrentPassword(password);
  }

  Future<bool> deactivateAccountAndLogout() async {
    try {
      await _accountRepository.deactivateAccount();
      await Modular.get<AuthenticationRepository>().logOut();
      Modular.to.navigate('/');
      return true;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return false;
    }
  }

  Future<void> deleteAccount() async {}

  Future<void> getTrafficSources() async {
    try {
      final List<SocialInfo> trafficSources = await _accountRepository.getTrafficSources();
      emit(state.copyWith(trafficSources: trafficSources));
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  Future<void> getInterestedFields() async {
    try {
      final siteId = (await commonCubit.sharedPreferencesService.getCurrentSiteId())!;
      final result = await _accountRepository.getInterestedFields(siteId);
      emit(state.copyWith(interestedFields: result));
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  Future<void> updateInterestedFields(List<PassionateItem> interestedFields) async {
    try {
      final siteId = (await commonCubit.sharedPreferencesService.getCurrentSiteId())!;
      List<int> categoryIds = interestedFields.map((e) => e.id).toList();
      await _accountRepository.upsertInterestedFields(siteId, categoryIds);

      await _clearInterestedFieldsRelatedCaches(siteId);
      emit(state.copyWith(interestedFields: interestedFields));
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  Future<bool> verifyPaymentOtp(String otp) async {
    try {
      final result = await _accountRepository.verifyPaymentOtp(otp);
      await storage.write(key: InstanceConstants.paymentOtpTokenKey, value: result['otpToken']);
      emit(state.copyWith(paymentOtpToken: result['otpToken']));
      return true;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return false;
    }
  }

  /// Comprehensive refresh method for pull-to-refresh functionality
  /// This method refreshes account and traffic sources data while bypassing cache to ensure fresh data
  /// Uses isPullToRefresh flag to prevent multiple loading indicators
  Future<void> pullToRefresh() async {
    try {
      emit(state.copyWith(isPullToRefresh: true));

      final apiService = Modular.get<ApiService>();

      await apiService.clearCacheForEndpoint('/v3/publishers/me/account');
      await apiService.clearCacheForEndpoint('/v3/publishers/me/sites');

      await Future.wait([
        getAccount(),
        getTrafficSources(),
      ]);
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    } finally {
      emit(state.copyWith(isPullToRefresh: false));
    }
  }

  /// Clear cache for interested fields and campaign-related endpoints to ensure data consistency
  /// This ensures that campaign recommendations are updated immediately after interest changes
  Future<void> _clearInterestedFieldsRelatedCaches(int siteId) async {
    try {
      final apiService = Modular.get<ApiService>();

      await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/categories');

      await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/campaigns/featured-summary');
      await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/campaigns/top-summary');
      await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/campaigns/fastest-growing-summary');
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }
}
