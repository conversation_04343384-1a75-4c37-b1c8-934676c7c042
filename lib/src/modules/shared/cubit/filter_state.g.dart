// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'filter_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$FilterStateImpl _$$FilterStateImplFromJson(Map<String, dynamic> json) =>
    _$FilterStateImpl(
      periods: (json['periods'] as List<dynamic>?)
              ?.map((e) => $enumDecode(_$ReportPeriodEnumMap, e))
              .toList() ??
          const [],
      selectedPeriod:
          $enumDecodeNullable(_$ReportPeriodEnumMap, json['selectedPeriod']) ??
              ReportPeriod.LAST_12_MONTHS,
      startDate: json['startDate'] == null
          ? null
          : DateTime.parse(json['startDate'] as String),
      endDate: json['endDate'] == null
          ? null
          : DateTime.parse(json['endDate'] as String),
      customStartDate: json['customStartDate'] == null
          ? null
          : DateTime.parse(json['customStartDate'] as String),
      customEndDate: json['customEndDate'] == null
          ? null
          : DateTime.parse(json['customEndDate'] as String),
      selectedDateType: $enumDecodeNullable(
              _$ReportQueryPeriodBaseEnumMap, json['selectedDateType']) ??
          ReportQueryPeriodBase.CONVERSION_DATE,
      selectedStatus: $enumDecodeNullable(
          _$ConversionStatusEnumMap, json['selectedStatus']),
      campaigns: (json['campaigns'] as List<dynamic>?)
              ?.map((e) => Item.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      selectedCampaign: json['selectedCampaign'] == null
          ? null
          : Item.fromJson(json['selectedCampaign'] as Map<String, dynamic>),
      invoiceId: json['invoiceId'] as String? ?? '',
      customName: json['customName'] as String? ?? '',
      errorMessage: json['errorMessage'] as String? ?? '',
      selectedSite: json['selectedSite'] == null
          ? null
          : Item.fromJson(json['selectedSite'] as Map<String, dynamic>),
      isSearchEnabled: json['isSearchEnabled'] as bool? ?? true,
    );

Map<String, dynamic> _$$FilterStateImplToJson(_$FilterStateImpl instance) =>
    <String, dynamic>{
      'periods':
          instance.periods.map((e) => _$ReportPeriodEnumMap[e]!).toList(),
      'selectedPeriod': _$ReportPeriodEnumMap[instance.selectedPeriod]!,
      'startDate': instance.startDate?.toIso8601String(),
      'endDate': instance.endDate?.toIso8601String(),
      'customStartDate': instance.customStartDate?.toIso8601String(),
      'customEndDate': instance.customEndDate?.toIso8601String(),
      'selectedDateType':
          _$ReportQueryPeriodBaseEnumMap[instance.selectedDateType]!,
      'selectedStatus': _$ConversionStatusEnumMap[instance.selectedStatus],
      'campaigns': instance.campaigns,
      'selectedCampaign': instance.selectedCampaign,
      'invoiceId': instance.invoiceId,
      'customName': instance.customName,
      'errorMessage': instance.errorMessage,
      'selectedSite': instance.selectedSite,
      'isSearchEnabled': instance.isSearchEnabled,
    };

const _$ReportPeriodEnumMap = {
  ReportPeriod.TODAY: 'TODAY',
  ReportPeriod.YESTERDAY: 'YESTERDAY',
  ReportPeriod.LAST_7_DAYS: 'LAST_7_DAYS',
  ReportPeriod.LAST_14_DAYS: 'LAST_14_DAYS',
  ReportPeriod.LAST_30_DAYS: 'LAST_30_DAYS',
  ReportPeriod.THIS_WEEK: 'THIS_WEEK',
  ReportPeriod.LAST_WEEK: 'LAST_WEEK',
  ReportPeriod.THIS_MONTH: 'THIS_MONTH',
  ReportPeriod.LAST_MONTH: 'LAST_MONTH',
  ReportPeriod.LAST_3_MONTHS: 'LAST_3_MONTHS',
  ReportPeriod.LAST_6_MONTHS: 'LAST_6_MONTHS',
  ReportPeriod.LAST_12_MONTHS: 'LAST_12_MONTHS',
  ReportPeriod.NEXT_3_MONTHS: 'NEXT_3_MONTHS',
  ReportPeriod.NEXT_6_MONTHS: 'NEXT_6_MONTHS',
  ReportPeriod.THIS_YEAR: 'THIS_YEAR',
  ReportPeriod.LAST_YEAR: 'LAST_YEAR',
  ReportPeriod.CUSTOM_RANGE: 'CUSTOM_RANGE',
};

const _$ReportQueryPeriodBaseEnumMap = {
  ReportQueryPeriodBase.CONVERSION_DATE: 'CONVERSION_DATE',
  ReportQueryPeriodBase.CONFIRMATION_DATE: 'CONFIRMATION_DATE',
  ReportQueryPeriodBase.POSTBACK_ERROR_DATE: 'POSTBACK_ERROR_DATE',
  ReportQueryPeriodBase.UPDATE_DATE: 'UPDATE_DATE',
  ReportQueryPeriodBase.PAID_DATE: 'PAID_DATE',
};

const _$ConversionStatusEnumMap = {
  ConversionStatus.PENDING: 'PENDING',
  ConversionStatus.APPROVED: 'APPROVED',
  ConversionStatus.REJECTED: 'REJECTED',
};
