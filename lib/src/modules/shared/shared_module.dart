import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/network/dio_client.dart';
import 'package:koc_app/src/modules/account/data/repository/account_repository.dart';
import 'package:koc_app/src/modules/authentication/cubit/authentication_cubit.dart';
import 'package:koc_app/src/modules/authentication/data/repository/authentication_repository.dart';
import 'package:koc_app/src/modules/campaign/data/repository/campaign_repository.dart';
import 'package:koc_app/src/modules/shared/cubit/common_bool_cubit.dart';
import 'package:koc_app/src/modules/shared/cubit/common_int_cubit.dart';
import 'package:koc_app/src/modules/shared/cubit/filter_cubit.dart';
import 'package:koc_app/src/modules/shared/cubit/pagination_cubit.dart';
import 'package:koc_app/src/modules/shared/repositories/currency_repository.dart';
import 'package:koc_app/src/shared/services/local_auth_service.dart';
import 'package:koc_app/src/shared/services/shared_preferences_service.dart';
import 'package:local_auth/local_auth.dart';

import '../../shared/services/api_service.dart';

class SharedModule extends Module {
  @override
  void exportedBinds(Injector i) {
    super.exportedBinds(i);
    i.addSingleton(DioClient.new);
    i.addLazySingleton(SharedPreferencesService.new);
    i.addLazySingleton(LocalAuthentication.new);
    i.addLazySingleton(LocalAuthService.new);
    i.addLazySingleton(AuthenticationCubit.new);
    i.addLazySingleton(AuthenticationRepository.new);
    i.addLazySingleton(CommonBoolCubit.new);
    i.addLazySingleton(CommonIntCubit.new);
    i.addLazySingleton(ApiService.new);
    i.addLazySingleton(FilterCubit.new);
    i.addLazySingleton(CampaignRepository.new);
    i.addLazySingleton(CurrencyRepository.new);
    i.addLazySingleton(AccountRepository.new);
    i.add(PaginationCubit.new);
  }
}
