import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/account/cubit/account_cubit.dart';
import 'package:koc_app/src/modules/authentication/cubit/authentication_cubit.dart';
import 'package:koc_app/src/modules/authentication/data/models/otp.dart';
import 'package:koc_app/src/modules/shared/model/otp_endpoint.dart';
import 'package:koc_app/src/modules/shared/widget/otp_timer_field.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koc_app/src/modules/shared/cubit/otp_timer_cubit.dart';
import '../../account/settings/cubit/account_settings_cubit.dart';

class VerifyIdentityPage extends StatefulWidget {
  final String verifyTarget;
  const VerifyIdentityPage({super.key, required this.verifyTarget});

  @override
  State<VerifyIdentityPage> createState() => _VerifyIdentityPageState();
}

class _VerifyIdentityPageState extends BasePageState<VerifyIdentityPage, AccountCubit> {
  bool _initialized = false;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_initialized) {
      final authenticationCubit = Modular.get<AuthenticationCubit>();
      Object? otpEndpoint = ModalRoute.of(context)?.settings.arguments;
      if (otpEndpoint == OtpEndpoint.showPayment) {
        authenticationCubit.sendPaymentOtp();
      }
      if (otpEndpoint == OtpEndpoint.resetPassword) {
        cubit.sendOtpResetPassword(widget.verifyTarget);
      }
      _initialized = true;
    }
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: const CommonAppBar(title: Text('Verify your identity')),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return BlocProvider<OtpTimerCubit>.value(
      value: Modular.get<OtpTimerCubit>(),
      child: Padding(
        padding: EdgeInsets.only(top: 16.r, left: 16.r, right: 16.r),
        child: OtpTimerField(
          otpType: getOtpType(),
          verifyTarget: widget.verifyTarget,
          btnName: 'Continue',
          showLoading: () => commonCubit.showLoading(),
          hideLoading: () => commonCubit.hideLoading(),
          onTap: (otp) async {
            commonCubit.showLoading();
            Object? otpEndpoint = ModalRoute.of(context)?.settings.arguments;
            if (otpEndpoint == OtpEndpoint.showPayment) {
              final accountSettingsCubit = Modular.get<AccountSettingsCubit>();
              final isVerified = await cubit.verifyPaymentOtp(otp);
              if (!isVerified) {
                context.showSnackBar(cubit.state.errorMessage);
              } else {
                await accountSettingsCubit.findAccount();
                final isBankAccountValid = accountSettingsCubit.state.isBankAccountValid;
                if (isBankAccountValid) {
                  Modular.to.pushNamed((otpEndpoint as OtpEndpoint).path);
                } else {
                  Modular.to.pushNamed('/account/settings/edit-payment');
                }
              }
            } else if (isEmail()) {
              final result = await cubit.verifyOtpResetPassword(widget.verifyTarget, otp);
              if (mounted) {
                if (result) {
                  Object? otpEndpoint = ModalRoute.of(context)?.settings.arguments;
                  if (otpEndpoint == null) {
                    Modular.to.popUntil(ModalRoute.withName('/account/settings/'));
                  } else {
                    Modular.to.pushNamed((otpEndpoint as OtpEndpoint).path);
                  }
                } else {
                  context.showSnackBar(cubit.state.errorMessage);
                }
              }
            }
            commonCubit.hideLoading();
          },
        ),
      ),
    );
  }

  bool isEmail() {
    return widget.verifyTarget.contains('@');
  }

  String getOtpType() {
    Object? otpEndpoint = ModalRoute.of(context)?.settings.arguments;
    if (otpEndpoint == OtpEndpoint.showPayment) {
      return OtpType.showPayment.name;
    }
    if (otpEndpoint == OtpEndpoint.resetPassword) {
      return OtpType.changePassword.name;
    }
    return OtpType.registration.name;
  }
}
