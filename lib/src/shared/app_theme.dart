import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

const String kFontFamily = 'NotoSans';

class AppTheme {
  static ThemeData lightTheme = ThemeData.light().copyWith(
      checkboxTheme: CheckboxThemeData(
        fillColor: WidgetStateProperty.resolveWith<Color?>((Set<WidgetState> states) {
          if (states.contains(WidgetState.selected)) {
            return const Color(0xFFFFB522);
          }
          return Colors.white;
        }),
      ),
      appBarTheme: AppBarTheme(
          backgroundColor: Colors.white,
          titleTextStyle: TextStyle(
              fontSize: 18.r, fontWeight: FontWeight.w500, fontFamily: kFontFamily, color: const Color(0xFF464646)),
          actionsIconTheme: IconThemeData(size: 20.r)),
      textTheme: TextTheme(
        headlineMedium: TextStyle(
            fontSize: 18.r, fontWeight: FontWeight.w700, fontFamily: kFontFamily, color: const Color(0xFF464646)),
        titleLarge: TextStyle(
            fontSize: 26.r, fontWeight: FontWeight.bold, fontFamily: kFontFamily, color: const Color(0xFF464646)),
        titleMedium: TextStyle(
            fontSize: 22.r, fontWeight: FontWeight.bold, fontFamily: kFontFamily, color: const Color(0xFF464646)),
        titleSmall: TextStyle(
            fontSize: 18.r, fontWeight: FontWeight.bold, fontFamily: kFontFamily, color: const Color(0xFF464646)),
        bodyLarge: TextStyle(
            fontSize: 24.r, fontFamily: kFontFamily, color: const Color(0xFF464646), fontWeight: FontWeight.w400),
        bodyMedium: TextStyle(
            fontSize: 20.r, fontFamily: kFontFamily, color: const Color(0xFF464646), fontWeight: FontWeight.w400),
        bodySmall: TextStyle(
            fontSize: 16.r, fontFamily: kFontFamily, color: const Color(0xFF464646), fontWeight: FontWeight.w400),
        labelLarge: TextStyle(
            fontSize: 14.r, fontFamily: kFontFamily, color: const Color(0xFF464646), fontWeight: FontWeight.w400),
        labelMedium: TextStyle(
            fontSize: 12.r, fontFamily: kFontFamily, color: const Color(0xFF464646), fontWeight: FontWeight.w400),
        labelSmall: TextStyle(
            fontSize: 10.r, fontFamily: kFontFamily, color: const Color(0xFF464646), fontWeight: FontWeight.w400),
      ),
      inputDecorationTheme: InputDecorationTheme(
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(9999.r),
          borderSide: const BorderSide(
            color: Color(0xFFE7E7E7),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(20.r),
          borderSide: const BorderSide(color: Color(0x4CEF6507)),
        ),
        labelStyle: TextStyle(fontSize: 16.r, fontFamily: kFontFamily, color: const Color(0xFF464646)),
        hintStyle: TextStyle(fontSize: 16.r, fontFamily: kFontFamily, color: const Color(0xFF464646)),
      ),
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        selectedIconTheme: IconThemeData(size: 24.r),
        unselectedIconTheme: IconThemeData(size: 24.r),
        unselectedLabelStyle:
            TextStyle(fontSize: 12.r, fontFamily: kFontFamily, fontWeight: FontWeight.w500, letterSpacing: 0.3.r),
        backgroundColor: Colors.white,
        unselectedItemColor: const Color(0xFF464646),
        selectedItemColor: const Color(0xFFEF6507),
      ),
      scaffoldBackgroundColor: Colors.white,
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFEF6507),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20.r),
            ),
            textStyle:
                TextStyle(fontSize: 14.r, color: Colors.white, fontFamily: kFontFamily, fontWeight: FontWeight.w500),
            padding: EdgeInsets.symmetric(vertical: 8.r, horizontal: 12.r)),
      ),
      tabBarTheme: TabBarThemeData(
        labelStyle: TextStyle(
            fontSize: 14.r, fontFamily: kFontFamily, fontWeight: FontWeight.w500, color: const Color(0xFF464646)),
        unselectedLabelStyle: TextStyle(
            fontSize: 12.r, fontFamily: kFontFamily, fontWeight: FontWeight.w400, color: const Color(0xFF464646)),
        tabAlignment: TabAlignment.start,
        indicatorColor: const Color(0xFFEF8A13),
        labelColor: const Color(0xFFEF8A13),
        unselectedLabelColor: const Color(0xFF999999),
      ));

  static ThemeData darkTheme = ThemeData.dark().copyWith(
    appBarTheme: AppBarTheme(
        backgroundColor: Colors.grey[800],
        toolbarHeight: 50.r,
        titleTextStyle: TextStyle(fontSize: 25.r, color: Colors.white),
        actionsIconTheme: IconThemeData(size: 30.r)),
    textTheme: TextTheme(
      bodyLarge: TextStyle(fontSize: 24.r, color: Colors.white),
      bodyMedium: TextStyle(fontSize: 20.r, color: Colors.white),
      bodySmall: TextStyle(fontSize: 16.r, color: Colors.white),
    ),
    inputDecorationTheme: InputDecorationTheme(
      border: const OutlineInputBorder(),
      labelStyle: TextStyle(fontSize: 20.r),
      hintStyle: TextStyle(fontSize: 20.r),
    ),
    bottomNavigationBarTheme: BottomNavigationBarThemeData(
      selectedIconTheme: IconThemeData(size: 25.r),
      unselectedIconTheme: IconThemeData(size: 25.r),
    ),
    scaffoldBackgroundColor: Colors.grey[800],
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.black,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10.r),
        ),
        textStyle: TextStyle(fontSize: 20.r, color: Colors.white),
      ),
    ),
  );
}
