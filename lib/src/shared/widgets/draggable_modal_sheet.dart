import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter/widgets.dart';

/// A reusable draggable modal sheet that can be pulled up/down.
/// Supports velocity-based gestures and provides smooth animations.
class DraggableModalSheet extends StatefulWidget {
  /// The content to display in the modal.
  final Widget Function(double modalHeight) child;

  /// The initial height of the modal, from 0.0 to 1.0.
  /// Default is 1.0 (full height).
  final double initialHeight;

  /// The minimum height of the modal, from 0.0 to 1.0.
  /// Default is 0.1.
  final double minHeight;

  /// The velocity threshold for closing the modal.
  /// Default is 300.0.
  final double closeVelocityThreshold;

  /// The height threshold for closing the modal.
  /// Default is 0.3.
  final double closeHeightThreshold;

  /// The height threshold for switching to full height.
  /// Default is 0.75.
  final double fullHeightThreshold;

  /// The mid-height snap point.
  /// Default is 0.5.
  final double midHeight;

  /// Whether to enable snap to mid height.
  /// Default is false.
  final bool snapToMid;

  /// The animation duration for height changes.
  /// Default is 200 milliseconds.
  final Duration animationDuration;

  /// The curve to use for animations.
  /// Default is Curves.easeOutCubic.
  final Curve animationCurve;

  /// Function to call when the modal is closed by dragging.
  final VoidCallback? onClose;

  /// The height of the drag handle area.
  /// Default is 48.0 logical pixels.
  final double dragHandleHeight;

  /// The color of the drag handle.
  /// Default is Color(0xFFDEDEDE).
  final Color dragHandleColor;

  /// Whether to enable toggle behavior when tapping the drag handle.
  /// When true, tapping the handle will toggle between full height and mid height.
  /// Default is false.
  final bool enableDragHandleTap;

  /// Whether to enable the "brag up to expand" feature.
  /// Default is false.
  final bool swipeUpToExpand;

  const DraggableModalSheet({
    super.key,
    required this.child,
    this.initialHeight = 1.0,
    this.minHeight = 0.1,
    this.closeVelocityThreshold = 300.0,
    this.closeHeightThreshold = 0.3,
    this.fullHeightThreshold = 0.75,
    this.midHeight = 0.5,
    this.snapToMid = false,
    this.animationDuration = const Duration(milliseconds: 200),
    this.animationCurve = Curves.easeOutCubic,
    this.onClose,
    this.dragHandleHeight = 48.0,
    this.dragHandleColor = const Color(0xFFDEDEDE),
    this.enableDragHandleTap = false,
    this.swipeUpToExpand = false,
  });

  @override
  State<DraggableModalSheet> createState() => _DraggableModalSheetState();
}

class _DraggableModalSheetState extends State<DraggableModalSheet> with SingleTickerProviderStateMixin {
  late double modalHeight;
  double? _startDragY;
  double? _startDragHeight;
  ScrollController? _scrollController;
  AnimationController? _expandController;
  Animation<double>? _expandAnimation;
  bool _isAnimatingExpand = false;

  @override
  void initState() {
    super.initState();
    modalHeight = widget.initialHeight.clamp(widget.minHeight, 1.0);
    if (widget.swipeUpToExpand) {
      _scrollController = ScrollController();
      _expandController = AnimationController(
        vsync: this,
        duration: const Duration(milliseconds: 300),
      );
    }
  }

  @override
  void dispose() {
    _scrollController?.dispose();
    _expandController?.dispose();
    super.dispose();
  }

  

  void _animateExpandToFullHeight() {
    if (_isAnimatingExpand || modalHeight >= 0.99) return;
    _isAnimatingExpand = true;
    _expandAnimation = Tween<double>(begin: modalHeight, end: 1.0).animate(CurvedAnimation(
      parent: _expandController!,
      curve: Curves.easeOutCubic,
    ))
      ..addListener(() {
        setState(() {
          modalHeight = _expandAnimation!.value;
        });
      })
      ..addStatusListener((status) {
        if (status == AnimationStatus.completed || status == AnimationStatus.dismissed) {
          _isAnimatingExpand = false;
        }
      });
    _expandController!.forward(from: 0);
  }

  void _snapToHeight() {
    if (modalHeight < widget.closeHeightThreshold) {
      if (widget.onClose != null) {
        widget.onClose!();
      } else {
        Navigator.of(context).pop();
      }
      return;
    }

    if (widget.snapToMid) {
      if (modalHeight < widget.fullHeightThreshold) {
        modalHeight = widget.midHeight;
      } else {
        modalHeight = 1.0;
      }
    } else {
      final midPoint = (widget.closeHeightThreshold + widget.fullHeightThreshold) / 2;

      if (modalHeight < midPoint) {
        if (modalHeight < widget.closeHeightThreshold) {
          if (widget.onClose != null) {
            widget.onClose!();
          } else {
            Navigator.of(context).pop();
          }
        } else {
          modalHeight = widget.midHeight;
        }
      } else {
        if (modalHeight < widget.fullHeightThreshold) {
          modalHeight = widget.midHeight;
        } else {
          modalHeight = 1.0;
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;

    return GestureDetector(
      onVerticalDragStart: (details) {
        _startDragY = details.globalPosition.dy;
        _startDragHeight = modalHeight;
      },
      onVerticalDragUpdate: (details) {
        if (_startDragY == null || _startDragHeight == null) return;

        final currentY = details.globalPosition.dy;
        final dragDistance = currentY - _startDragY!;
        final heightChange = dragDistance / screenHeight;

        final adjustedChange = heightChange < 0 ? heightChange * 1.2 : heightChange;

        setState(() {
          modalHeight = (_startDragHeight! - adjustedChange).clamp(widget.minHeight, 1.0);
        });
      },
      onVerticalDragEnd: (details) {
        _startDragY = null;
        _startDragHeight = null;

        final velocity = details.primaryVelocity ?? 0;
        setState(() {
          if (velocity > widget.closeVelocityThreshold) {
            if (widget.snapToMid && modalHeight > widget.midHeight) {
              modalHeight = widget.midHeight;
            } else {
              if (widget.onClose != null) {
                widget.onClose!();
              } else {
                Navigator.of(context).pop();
              }
            }
          } else if (velocity < -widget.closeVelocityThreshold) {
            if (widget.snapToMid && modalHeight < widget.midHeight) {
              modalHeight = widget.midHeight;
            } else {
              modalHeight = 1.0;
            }
          } else {
            _snapToHeight();
          }
        });
      },
      child: AnimatedContainer(
        duration: widget.animationDuration,
        curve: widget.animationCurve,
        height: screenHeight * modalHeight,
        child: Column(
          children: [
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: widget.enableDragHandleTap
                  ? () {
                      setState(() {
                        modalHeight = modalHeight < 0.9 ? 1.0 : widget.midHeight;
                      });
                    }
                  : null,
              child: Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(vertical: 8.r),
                child: Center(
                  child: Container(
                    margin: EdgeInsets.symmetric(vertical: 4.r),
                    width: 72.r,
                    height: 4.r,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(9999.r),
                      color: widget.dragHandleColor,
                    ),
                  ),
                ),
              ),
            ),
            // Content
            Expanded(
              child: widget.swipeUpToExpand
                  ? NotificationListener<ScrollNotification>(
                      onNotification: (notification) {
                        if (notification is ScrollUpdateNotification &&
                            notification.scrollDelta != null &&
                            notification.scrollDelta! > 15 &&
                            modalHeight < 0.99 &&
                            !_isAnimatingExpand) {
                          _animateExpandToFullHeight();
                        }
                        return false;
                      },
                      child: Builder(
                        builder: (context) => widget.child(modalHeight),
                      ),
                    )
                  : widget.child(modalHeight),
            ),
          ],
        ),
      ),
    );
  }
}
