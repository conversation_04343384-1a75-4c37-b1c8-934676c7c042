import 'package:flutter/material.dart';

/// Chart styling constants for consistent appearance across the app
class ChartConstants {
  const ChartConstants._();

  static const Color leftLineColor = Color(0xFF1AAA55);
  static const Color rightLineColor = Color(0xFFFFB522);
  static const Color tooltipBackgroundColor = Color(0xFFF5F5F7);

  static const double lineWidth = 0.75;   
  static const double dotRadius = 4.0;
  static const double dotStrokeWidth = 2.0;
  static const Color dotStrokeColor = Colors.white;

  static const double areaFillOpacity = 0.2;

  static const double chartAspectRatio = 1.5;
  static const bool showBorder = false;
  static const bool enableTouch = true;
  static const bool showCurvedLines = true;
  static const bool showDots = true;
  static const bool showAreaFill = true;

  /// -45 degrees (π/4)
  static const double xAxisLabelRotation = -0.785398;
}
