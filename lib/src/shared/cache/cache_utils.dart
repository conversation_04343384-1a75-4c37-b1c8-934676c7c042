import 'package:dio/dio.dart';

import 'cache_config.dart';
import 'cache_interceptor.dart';
import 'cache_manager.dart';

/// Utility class for cache operations
class CacheUtils {
  /// Force caching for a request
  static Map<String, dynamic> forceCache(Map<String, dynamic> extra) {
    return {
      ...extra,
      CacheInterceptor.forceCacheKey: true,
    };
  }

  /// Set a custom TTL for a request
  static Map<String, dynamic> withTtl(Map<String, dynamic> extra, int ttl) {
    return {
      ...extra,
      CacheInterceptor.cacheTtlKey: ttl,
    };
  }

  /// Format cache size in a human-readable format
  static String formatCacheSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(2)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(2)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(2)} GB';
    }
  }

  /// Get cache statistics
  static Future<Map<String, dynamic>> getCacheStats() async {
    final cacheManager = CacheManager();
    await cacheManager.init();

    final size = await cacheManager.getCacheSize();
    final count = await cacheManager.getCacheEntryCount();
    final metrics = cacheManager.metrics;

    return {
      'size': size,
      'formattedSize': formatCacheSize(size),
      'entryCount': count,
      'hits': metrics.hits,
      'misses': metrics.misses,
      'hitRate': metrics.hitRate,
      'writes': metrics.writes,
      'fallbacks': metrics.fallbacks,
      'errors': metrics.errors,
      'lastReset': metrics.lastResetDate.toIso8601String(),
    };
  }

  /// Add cache interceptor to a Dio instance
  static void addCacheInterceptor(Dio dio, {CacheConfig? config}) {
    // Check if the interceptor is already added
    final hasInterceptor = dio.interceptors.any((i) => i is CacheInterceptor);
    if (!hasInterceptor) {
      dio.interceptors.add(CacheInterceptor(config: config));
    }
  }

  /// Clear the entire cache
  static Future<void> clearCache() async {
    final cacheManager = CacheManager();
    await cacheManager.clearCache();
  }

  /// Clear specific endpoints from the cache
  static Future<void> clearEndpoint(String endpoint) async {
    final cacheManager = CacheManager();
    await cacheManager.clearEndpoint(endpoint);
  }

  /// Warm up the cache for specific endpoints
  static Future<void> warmCache(List<String> endpoints, Dio dio) async {
    final cacheManager = CacheManager();
    await cacheManager.warmCache(endpoints, dio);
  }

  /// Reset cache metrics
  static Future<void> resetMetrics() async {
    final cacheManager = CacheManager();
    await cacheManager.init();
    cacheManager.metrics.reset();
  }
}
