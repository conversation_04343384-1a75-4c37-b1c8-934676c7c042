import 'package:dio/dio.dart';

import 'cache_config.dart';
import 'cache_interceptor.dart';
import 'cache_manager.dart';
import 'cache_metrics.dart';
import 'unified_cache_manager.dart';

/// A service for managing the unified caching system (API + Images)
class CacheService {
  final UnifiedCacheManager _unifiedCacheManager;
  late Dio _dio;

  /// Singleton instance
  static final CacheService _instance = CacheService._internal();

  /// Factory constructor to return the singleton instance
  factory CacheService({required Dio dio}) {
    _instance._dio = dio;
    return _instance;
  }

  /// Private constructor
  CacheService._internal() : _unifiedCacheManager = UnifiedCacheManager();

  /// Get the legacy cache manager for backward compatibility
  CacheManager get _cacheManager => _unifiedCacheManager.apiCacheManager;

  /// Initialize the cache service
  Future<void> init() async {
    await _unifiedCacheManager.init();
    
    _dio.interceptors.removeWhere((i) => i is CacheInterceptor);
    
    _dio.interceptors.insert(0, CacheInterceptor(
      cacheManager: _cacheManager,
      config: _unifiedCacheManager.config,
    ));
  }

  /// Get the current cache configuration
  CacheConfig get config => _unifiedCacheManager.config;

  /// Get the current cache metrics
  CacheMetrics get metrics => _cacheManager.metrics;

  /// Get the unified cache manager
  UnifiedCacheManager get unifiedCacheManager => _unifiedCacheManager;

  /// Update the cache configuration
  Future<void> updateConfig(CacheConfig newConfig) async {
    await _unifiedCacheManager.updateConfig(newConfig);

    _dio.interceptors.removeWhere((i) => i is CacheInterceptor);
    
    _dio.interceptors.insert(0, CacheInterceptor(
      cacheManager: _cacheManager,
      config: newConfig,
    ));
  }

  /// Clear the entire cache (both API and images)
  Future<void> clearCache() async {
    await _unifiedCacheManager.clearAllCache();
  }

  /// Force clear cache configuration from storage
  Future<void> clearCacheConfig() async {
    await _cacheManager.clearCacheConfig();
  }

  /// Clear only API cache
  Future<void> clearApiCache() async {
    await _unifiedCacheManager.clearApiCache();
  }

  /// Clear only image cache
  Future<void> clearImageCache() async {
    await _unifiedCacheManager.clearImageCache();
  }

  /// Clear specific endpoints from the cache
  Future<void> clearEndpoint(String endpoint) async {
    await _unifiedCacheManager.clearApiEndpoint(endpoint);
  }

  /// Clear all site-specific cache entries for a given site ID
  /// This ensures data consistency when switching between sites
  Future<void> clearSiteSpecificCache(int siteId) async {
    await _unifiedCacheManager.clearSiteSpecificCache(siteId);
  }

  /// Get comprehensive cache statistics (API + Images)
  Future<Map<String, dynamic>> getCacheStats() async {
    return await _unifiedCacheManager.getCacheStats();
  }

  /// Warm up the cache for specific endpoints
  Future<void> warmCache(List<String> endpoints) async {
    await _cacheManager.warmCache(endpoints, _dio);
  }

  /// Enable caching
  Future<void> enableCaching() async {
    final newConfig = config.copyWith(enableCaching: true);
    await updateConfig(newConfig);
  }

  /// Disable caching
  Future<void> disableCaching() async {
    final newConfig = config.copyWith(enableCaching: false);
    await updateConfig(newConfig);
  }

  /// Set the default TTL
  Future<void> setDefaultTtl(int ttl) async {
    final newConfig = config.copyWith(defaultTtl: ttl);
    await updateConfig(newConfig);
  }

  /// Set TTL for a specific endpoint
  Future<void> setEndpointTtl(String endpoint, int ttl) async {
    final endpointTtls = Map<String, int>.from(config.endpointTtls);
    endpointTtls[endpoint] = ttl;

    final newConfig = config.copyWith(endpointTtls: endpointTtls);
    await updateConfig(newConfig);
  }

  /// Add an endpoint to the exclusion list
  Future<void> excludeEndpoint(String endpoint) async {
    final excludedEndpoints = List<String>.from(config.excludedEndpoints);
    if (!excludedEndpoints.contains(endpoint)) {
      excludedEndpoints.add(endpoint);

      final newConfig = config.copyWith(excludedEndpoints: excludedEndpoints);
      await updateConfig(newConfig);
    }
  }

  /// Remove an endpoint from the exclusion list
  Future<void> includeEndpoint(String endpoint) async {
    final excludedEndpoints = List<String>.from(config.excludedEndpoints);
    excludedEndpoints.remove(endpoint);

    final newConfig = config.copyWith(excludedEndpoints: excludedEndpoints);
    await updateConfig(newConfig);
  }

  /// Add an endpoint to the cache-on-error list
  Future<void> addCacheOnErrorEndpoint(String endpoint) async {
    final cacheOnErrorEndpoints = List<String>.from(config.cacheOnErrorEndpoints);
    if (!cacheOnErrorEndpoints.contains(endpoint)) {
      cacheOnErrorEndpoints.add(endpoint);

      final newConfig = config.copyWith(cacheOnErrorEndpoints: cacheOnErrorEndpoints);
      await updateConfig(newConfig);
    }
  }

  /// Remove an endpoint from the cache-on-error list
  Future<void> removeCacheOnErrorEndpoint(String endpoint) async {
    final cacheOnErrorEndpoints = List<String>.from(config.cacheOnErrorEndpoints);
    cacheOnErrorEndpoints.remove(endpoint);

    final newConfig = config.copyWith(cacheOnErrorEndpoints: cacheOnErrorEndpoints);
    await updateConfig(newConfig);
  }

  /// Set the maximum cache size (legacy method - sets API cache size)
  Future<void> setMaxCacheSize(int maxSizeBytes) async {
    final newConfig = config.copyWith(maxApiCacheSize: maxSizeBytes);
    await updateConfig(newConfig);
  }

  /// Set maximum cache sizes for API, images, and total
  Future<void> setMaxCacheSizes({
    int? maxApiCacheSize,
    int? maxImageCacheSize,
    int? maxTotalCacheSize,
  }) async {
    await _unifiedCacheManager.setMaxCacheSizes(
      maxApiCacheSize: maxApiCacheSize,
      maxImageCacheSize: maxImageCacheSize,
      maxTotalCacheSize: maxTotalCacheSize,
    );
  }

  /// Set cache cleanup strategy
  Future<void> setCleanupStrategy(CacheCleanupStrategy strategy) async {
    await _unifiedCacheManager.setCleanupStrategy(strategy);
  }

  /// Enable or disable image caching
  Future<void> setEnableImageCaching(bool enable) async {
    final newConfig = config.copyWith(enableImageCaching: enable);
    await updateConfig(newConfig);
  }

  /// Set default TTL for images
  Future<void> setDefaultImageTtl(int ttlSeconds) async {
    final newConfig = config.copyWith(defaultImageTtl: ttlSeconds);
    await updateConfig(newConfig);
  }

  /// Enable or disable serving stale data on error
  Future<void> setServeStaleDataOnError(bool enable) async {
    final newConfig = config.copyWith(serveStaleDataOnError: enable);
    await updateConfig(newConfig);
  }

  /// Enable or disable caching POST requests
  Future<void> setCachePostRequests(bool enable) async {
    final newConfig = config.copyWith(cachePostRequests: enable);
    await updateConfig(newConfig);
  }

  /// Reset cache metrics
  Future<void> resetMetrics() async {
    metrics.reset();
  }

  /// Get all cache keys with detailed information
  Future<List<Map<String, dynamic>>> getAllCacheKeys() async {
    return await _cacheManager.getAllCacheKeys();
  }

  /// Get all cached image objects with detailed information
  Future<List<Map<String, dynamic>>> getAllImageCacheObjects() async {
    return await _unifiedCacheManager.getAllImageCacheObjects();
  }

  /// Remove a specific image from cache
  Future<void> removeImageFromCache(String url) async {
    await _unifiedCacheManager.removeImageFromCache(url);
  }

  /// Enforce cache size limits
  Future<void> enforceCacheSizeLimits() async {
    await _unifiedCacheManager.enforceCacheSizeLimits();
  }
}
