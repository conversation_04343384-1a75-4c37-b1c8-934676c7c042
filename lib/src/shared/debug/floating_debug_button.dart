import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/shared/config/debug_config.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// A draggable floating button for accessing debug tools
///
/// The button is visually disabled (grayed out with reduced opacity) when on the debug page
/// but remains draggable for repositioning. When not on the debug page, it's fully enabled
/// and can be tapped to navigate to the debug page.
class FloatingDebugButton extends StatefulWidget {
  const FloatingDebugButton({super.key});

  @override
  State<FloatingDebugButton> createState() => _FloatingDebugButtonState();
}

class _FloatingDebugButtonState extends State<FloatingDebugButton> {
  double _xPosition = 20.r;
  double _yPosition = 50.r;
  bool _positionInitialized = false;
  bool _isOnDebugPage = false;
  Timer? _routeCheckTimer;

  static const String _xPositionKey = 'debug_button_x_position';
  static const String _yPositionKey = 'debug_button_y_position';

  @override
  void initState() {
    super.initState();
    _loadSavedPosition();
    _startRouteChecking();
  }

  @override
  void dispose() {
    _routeCheckTimer?.cancel();
    super.dispose();
  }

  void _startRouteChecking() {
    _routeCheckTimer = Timer.periodic(const Duration(milliseconds: 500), (timer) {
      if (!mounted) return;

      bool isOnDebug = false;
      try {
        final currentPath = Modular.to.path;
        isOnDebug = currentPath == '/debug';
      } catch (e) {
        try {
          final currentRoute = ModalRoute.of(context);
          final routeName = currentRoute?.settings.name;
          isOnDebug = routeName == '/debug';
        } catch (e2) {
          isOnDebug = false;
        }
      }

      if (isOnDebug != _isOnDebugPage) {
        setState(() {
          _isOnDebugPage = isOnDebug;
        });
      }
    });
  }

  Future<void> _loadSavedPosition() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      if (mounted) {
        setState(() {
          final savedX = prefs.getDouble(_xPositionKey);
          final savedY = prefs.getDouble(_yPositionKey);

          if (savedX != null && savedY != null) {
            _xPosition = savedX;
            _yPosition = savedY;
            _positionInitialized = true;
          }
        });
      }
    } catch (e) {}
  }

  Future<void> _savePosition() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble(_xPositionKey, _xPosition);
      await prefs.setDouble(_yPositionKey, _yPosition);
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    if (!DebugConfig.showDebugTools) {
      return const SizedBox.shrink();
    }

    if (_isOnDebugPage) {
      return const SizedBox.shrink();
    }

    final screenSize = MediaQuery.of(context).size;
    final buttonSize = 40.r;

    if (!_positionInitialized) {
      _xPosition = screenSize.width - 60.r;
      _yPosition = 50.r;
      _positionInitialized = true;
    }

    _xPosition = _xPosition.clamp(0.0, screenSize.width - buttonSize);
    _yPosition = _yPosition.clamp(0.0, screenSize.height - buttonSize);

    return Positioned(
      left: _xPosition,
      top: _yPosition,
      child: GestureDetector(
        onPanUpdate: (details) {
          setState(() {
            _xPosition = (_xPosition + details.delta.dx).clamp(0, screenSize.width - buttonSize);
            _yPosition = (_yPosition + details.delta.dy).clamp(0, screenSize.height - buttonSize);
          });
        },
        onPanEnd: (_) {
          _savePosition();
        },
        child: Material(
          type: MaterialType.transparency,
          child: Stack(
            alignment: Alignment.center,
            children: [
              Container(
                width: buttonSize * 1.2,
                height: buttonSize * 1.2,
                decoration: const BoxDecoration(
                  color: Colors.transparent,
                  shape: BoxShape.circle,
                ),
              ),
              FloatingActionButton(
                heroTag: 'debugButton',
                mini: true,
                backgroundColor: const Color.fromARGB(187, 255, 18, 1),
                foregroundColor: const Color.fromARGB(255, 255, 255, 255), // White icon
                onPressed: () {
                  Modular.to.pushNamed('/debug');
                },
                child: const Icon(Icons.bug_report, size: 20),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
