import 'dart:convert';
import 'dart:developer' as dev;

import 'package:app_tracking_transparency/app_tracking_transparency.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:koc_app/src/modules/authentication/data/models/auth_token_info.dart';
import 'package:koc_app/src/modules/authentication/data/models/otp.dart';
import 'package:koc_app/src/modules/authentication/data/models/sign_in_sign_up.dart';
import 'package:koc_app/src/modules/authentication/data/repository/authentication_repository.dart';
import 'package:koc_app/src/shared/constants/instance_key.dart';
import 'package:koc_app/src/shared/services/shared_preferences_service.dart';

class FacebookAuthService {
  static const storage = FlutterSecureStorage();
  final AuthenticationRepository _authenticationRepository;
  final SharedPreferencesService _sharedPreferencesService;

  static const String _tokenTypeKey = 'facebook_token_type';
  static const String _standardTokenType = 'standard';
  static const String _limitedTokenType = 'limited';

  FacebookAuthService(this._authenticationRepository, this._sharedPreferencesService);

  Future<void> requestATT() async {
    final status = await AppTrackingTransparency.trackingAuthorizationStatus;

    if (status == TrackingStatus.notDetermined) {
      final newStatus = await AppTrackingTransparency.requestTrackingAuthorization();
      dev.log("ATT status: $newStatus");
    } else {
      dev.log("ATT status already determined: $status");
    }
  }

  Future<FacebookAuthResponse?> signInWithFacebook() async {
    try {
      await FacebookAuth.instance.logOut();
      await requestATT();

      final LoginResult result = await FacebookAuth.instance.login(
        permissions: ['public_profile', 'email'],
        loginBehavior: LoginBehavior.nativeWithFallback,
      );

      if (result.status == LoginStatus.success) {
        final accessTokenObj = result.accessToken!;
        String tokenType = _standardTokenType;
        String token = accessTokenObj.token;

        if (token.startsWith('EAA')) {
          dev.log('Detected standard User Access Token from Facebook');
        } else {
          tokenType = _limitedTokenType;
          dev.log('Unknown token format: ${token.substring(0, token.length > 10 ? 10 : token.length)}...');
        }

        await storage.write(key: _tokenTypeKey, value: tokenType);

        Map<String, dynamic> userData;
        try {
          userData = await FacebookAuth.instance.getUserData(
            fields: "id,name,first_name,last_name,email,picture.width(200)",
          );
        } catch (e) {
          dev.log('Error getting Facebook user data: $e');
          if (tokenType == _limitedTokenType) {
            dev.log('Using Limited Login token - attempting alternative user data retrieval');

            userData = {
              'id': accessTokenObj.userId ?? '',
              'name': null,
              'first_name': null,
              'last_name': null,
              'email': null,
              'picture': null,
            };
          } else {
            dev.log('Error getting Facebook user data: $e');
            return null;
          }
        }

        final response = FacebookAuthResponse(
          id: userData['id'] ?? '',
          first_name: userData['first_name'],
          last_name: userData['last_name'],
          name: userData['name'],
          email: userData['email'],
          picture: userData['picture'] != null
              ? FacebookPicture(
                  data: FacebookPictureData(
                    height: userData['picture']['data']['height'] ?? 0,
                    width: userData['picture']['data']['width'] ?? 0,
                    is_silhouette: userData['picture']['data']['is_silhouette'] ?? false,
                    url: userData['picture']['data']['url'] ?? '',
                  ),
                )
              : null,
          accessToken: token,
        );

        await storage.write(
          key: InstanceConstants.facebookAuthDataKey,
          value: jsonEncode({
            'id': response.id,
            'first_name': response.first_name,
            'last_name': response.last_name,
            'name': response.name,
            'email': response.email,
            'accessToken': response.accessToken,
            'tokenType': tokenType,
            'picture': response.picture != null
                ? {
                    'data': {
                      'height': response.picture!.data.height,
                      'width': response.picture!.data.width,
                      'is_silhouette': response.picture!.data.is_silhouette,
                      'url': response.picture!.data.url,
                    }
                  }
                : null,
          }),
        );

        return response;
      } else {
        dev.log('Facebook login failed: ${result.message}');
        return null;
      }
    } catch (e) {
      dev.log('Error signing in with Facebook: $e');
      return null;
    }
  }

  Future<void> signOut() async {
    try {
      await FacebookAuth.instance.logOut();
      await storage.delete(key: InstanceConstants.facebookAuthDataKey);
    } catch (e) {
      dev.log('Error during Facebook sign out: $e');
    }
  }

  Future<FacebookAuthResponse?> getFacebookAuthResponseFromStorage() async {
    try {
      final facebookAuthData = await storage.read(key: InstanceConstants.facebookAuthDataKey);
      if (facebookAuthData != null) {
        final data = jsonDecode(facebookAuthData);
        return FacebookAuthResponse(
          id: data['id'] ?? '',
          first_name: data['first_name'],
          last_name: data['last_name'],
          name: data['name'],
          email: data['email'],
          picture: data['picture'] != null
              ? FacebookPicture(
                  data: FacebookPictureData(
                    height: data['picture']['data']['height'] ?? 0,
                    width: data['picture']['data']['width'] ?? 0,
                    is_silhouette: data['picture']['data']['is_silhouette'] ?? false,
                    url: data['picture']['data']['url'] ?? '',
                  ),
                )
              : null,
          accessToken: data['accessToken'],
        );
      } else {
        return null;
      }
    } catch (e) {
      dev.log('Failed to retrieve Facebook data: $e');
      return null;
    }
  }

  Future<bool> processFacebookLogin(String accessToken, String countryCode, Function(String) onError) async {
    try {
      if (!await validateFacebookTokenAndData(accessToken, onError)) {
        return false;
      }

      final request = SocialSignInPayload(
          accessToken: accessToken, countryCode: countryCode, socialNetworkType: SocialNetworkType.facebook.value);

      await _authenticationRepository.socialLogin(request);
      return true;
    } catch (e) {
      if (e.toString().contains('token') || e.toString().contains('auth')) {
        onError("Facebook authentication failed. Please try again or use another login method.");
      } else {
        onError(e.toString());
      }
      return false;
    }
  }

  Future<bool> validateFacebookTokenAndData(String accessToken, Function(String) onError) async {
    try {
      if (accessToken.isEmpty) {
        onError("Facebook access token is invalid");
        return false;
      }

      final facebookAuthData = await getFacebookAuthResponseFromStorage();
      if (facebookAuthData == null || facebookAuthData.id.isEmpty) {
        onError("Facebook user data not found");
        return false;
      }

      final tokenTypeData = await storage.read(key: _tokenTypeKey);
      final isLimitedToken = tokenTypeData == _limitedTokenType;

      if (isLimitedToken) {
        dev.log('Limited Login token detected - backend does not support this token type');
        onError(
            "Your device privacy settings are preventing Facebook login. Please adjust your privacy settings or use another login method.");
        return false;
      }

      return true;
    } catch (e) {
      dev.log('Error validating Facebook token and data: $e');
      onError("Facebook validation failed. Please try again.");
      return false;
    }
  }

  Future<bool> handleFacebookVerifyOtp(
      String otp, String email, String countryCode, bool isRegistered, Function(String) onError) async {
    try {
      if (isRegistered) {
        final result = await _authenticationRepository.verifySignInOtpCode(email, countryCode, otp);
        final checkData = AuthTokenInfo.fromJson(result);
        if (checkData.token.isNotEmpty) {
          final facebookAuthData = await storage.read(key: InstanceConstants.facebookAuthDataKey);
          if (facebookAuthData != null) {
            final data = jsonDecode(facebookAuthData);
            final accessToken = data['accessToken'];
            final tokenType = data['tokenType'] ?? await storage.read(key: _tokenTypeKey) ?? _standardTokenType;

            if (accessToken != null && accessToken.toString().isNotEmpty) {
              dev.log('Processing Facebook OTP verification with token type: $tokenType');
              return await processFacebookLogin(accessToken, countryCode, onError);
            } else {
              onError("Facebook access token not found");
              return false;
            }
          } else {
            onError("Facebook authentication data not found");
            return false;
          }
        } else {
          return false;
        }
      } else {
        final facebookAuthData = await storage.read(key: InstanceConstants.facebookAuthDataKey);
        if (facebookAuthData == null) {
          onError("Facebook authentication data not found");
          return false;
        }

        final data = jsonDecode(facebookAuthData);
        final firstName = data['first_name'];
        final lastName = data['last_name'];
        final id = data['id'];
        final pictureData = data['picture'];
        final tokenType = data['tokenType'] ?? await storage.read(key: _tokenTypeKey) ?? _standardTokenType;
        final isLimitedToken = tokenType == _limitedTokenType;

        if (isLimitedToken) {
          dev.log('Processing Facebook signup with Limited Login token');

          final List<Future> futures = [];

          if (id != null) {
            futures.add(_sharedPreferencesService.setToInstance(InstanceConstants.facebookIdKey, id));
          }

          if (firstName != null) {
            futures.add(_sharedPreferencesService.setToInstance(InstanceConstants.firstNameKey, firstName));
          } else if (data['name'] != null) {
            final nameParts = data['name'].toString().split(' ');
            if (nameParts.isNotEmpty) {
              futures.add(_sharedPreferencesService.setToInstance(InstanceConstants.firstNameKey, nameParts[0]));
            }
          }

          if (lastName != null) {
            futures.add(_sharedPreferencesService.setToInstance(InstanceConstants.lastNameKey, lastName));
          } else if (data['name'] != null) {
            final nameParts = data['name'].toString().split(' ');
            if (nameParts.length > 1) {
              futures.add(_sharedPreferencesService.setToInstance(
                  InstanceConstants.lastNameKey, nameParts.sublist(1).join(' ')));
            }
          }

          if (pictureData != null && pictureData['data'] != null && pictureData['data']['url'] != null) {
            futures.add(_sharedPreferencesService.setToInstance(
                InstanceConstants.profilePictureUrlKey, pictureData['data']['url']));
          }

          if (futures.isNotEmpty) {
            await Future.wait(futures);
          }
        } else {
          if (firstName == null || lastName == null || id == null) {
            onError("Facebook user data is incomplete");
            return false;
          }

          await Future.wait([
            _sharedPreferencesService.setToInstance(InstanceConstants.firstNameKey, firstName),
            _sharedPreferencesService.setToInstance(InstanceConstants.lastNameKey, lastName),
            _sharedPreferencesService.setToInstance(InstanceConstants.facebookIdKey, id),
          ]);

          if (pictureData != null && pictureData['data'] != null && pictureData['data']['url'] != null) {
            await _sharedPreferencesService.setToInstance(
                InstanceConstants.profilePictureUrlKey, pictureData['data']['url']);
          }
        }

        VerifySignUpOtpRequest request = VerifySignUpOtpRequest(
          email: email,
          countryCode: countryCode,
          otp: otp,
          otpType: OtpType.registration,
        );
        await _authenticationRepository.verifySignUpOtpCode(request);
        return true;
      }
    } catch (e) {
      onError(e.toString());
      return false;
    }
  }
}
