import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:image_picker/image_picker.dart';
import 'package:koc_app/src/shared/cache/cache.dart';
import 'package:koc_app/src/shared/constants/instance_key.dart';
import 'package:koc_app/src/shared/services/secure_storage_helper.dart';
import 'package:koc_app/src/shared/services/shared_preferences_service.dart';
import 'package:path/path.dart' as file_path;

import '../../../network/dio_client.dart';

/// Service class for handling API requests with convenient methods for different HTTP methods
class ApiService {
  final DioClient dioClient;
  final SharedPreferencesService sharedPreferencesService;
  static final SecureStorageHelper storage = SecureStorageHelper();

  /// Access to the cache service
  CacheService get cacheService => dioClient.cacheService;

  ApiService(this.dioClient, this.sharedPreferencesService);

  // Use null-safe getters with default values for environment variables
  static String get baseUrl => dotenv.env['BASE_URL'] ?? '';
  static String get clientId => dotenv.env['CLIENT_ID'] ?? '';

  /// GET request with JWT authentication
  Future<dynamic> getData(String path, {Map<String, dynamic>? params}) async {
    try {
      final response = await dioClient.dio.get(
        '$baseUrl$path',
        queryParameters: params,
      );
      return response.data;
    } on DioException {
      rethrow;
    }
  }

  /// GET request with JWT authentication and get plain text response
  Future<dynamic> getPlainText(String path, {Map<String, dynamic>? params}) async {
    try {
      final response = await dioClient.dio.get(
        '$baseUrl$path',
        queryParameters: params,
        options: Options(headers: {'accept': 'text/plain'}),
      );
      return response.data;
    } on DioException {
      rethrow;
    }
  }

  /// GET request with JWT authentication and custom headers
  Future<dynamic> getDataWithHeaders(
    String path, {
    Map<String, dynamic>? params,
    Map<String, String>? headers,
  }) async {
    try {
      final accessToken = await storage.read(InstanceConstants.tokenKey);
      final Map<String, dynamic> allHeaders = {
        'Authorization': 'Bearer $accessToken',
        if (headers != null) ...headers,
      };

      final response = await dioClient.dio.get(
        '$baseUrl$path',
        queryParameters: params,
        options: Options(headers: allHeaders),
      );
      return response.data;
    } on DioException {
      rethrow;
    }
  }

  /// GET request without JWT authentication
  Future<dynamic> getDataWithoutJwt(String path, {Map<String, dynamic>? params}) async {
    try {
      final response = await dioClient.dio.get(
        '$baseUrl$path',
        queryParameters: params,
        options: Options(extra: {InstanceConstants.requiresTokenKey: false}),
      );
      return response.data;
    } on DioException {
      rethrow;
    }
  }

  /// POST request with JWT authentication from secure storage
  Future<dynamic> postData(String path, dynamic body) async {
    try {
      final accessToken = await storage.read(InstanceConstants.tokenKey);
      final response = await dioClient.dio.post(
        '$baseUrl$path',
        data: body,
        options: Options(headers: {
          'Authorization': 'Bearer $accessToken',
        }),
      );
      return response.data;
    } on DioException {
      rethrow;
    }
  }

  /// POST request with JWT authentication from secure storage and get response status code
  Future<int> postDataAndExtractStatusCode(String path, dynamic body) async {
    try {
      final accessToken = await storage.read(InstanceConstants.tokenKey);
      final response = await dioClient.dio.post(
        '$baseUrl$path',
        data: body,
        options: Options(headers: {
          'Authorization': 'Bearer $accessToken',
        }),
      );
      return response.statusCode ?? 0;
    } on DioException {
      rethrow;
    }
  }

  /// POST request with provided JWT token
  Future<dynamic> postDataWithTokenParam(String path, dynamic body, String token) async {
    try {
      final response = await dioClient.dio.post(
        '$baseUrl$path',
        data: body,
        options:
            Options(headers: {'Authorization': 'Bearer $token'}, extra: {InstanceConstants.requiresTokenKey: false}),
      );
      return response.data;
    } on DioException {
      rethrow;
    }
  }

  /// PUT request with provided JWT token
  Future<dynamic> putDataWithTokenParam(String path, dynamic body, String token) async {
    try {
      final response = await dioClient.dio.put(
        '$baseUrl$path',
        data: body,
        options:
            Options(headers: {'Authorization': 'Bearer $token'}, extra: {InstanceConstants.requiresTokenKey: false}),
      );
      return response.data;
    } on DioException {
      rethrow;
    }
  }

  /// POST request without JWT authentication
  Future<dynamic> postDataWithoutJwt(String path, dynamic body) async {
    try {
      final response = await dioClient.dio.post(
        '$baseUrl$path',
        data: body,
        options: Options(extra: {InstanceConstants.requiresTokenKey: false}),
      );
      return response.data;
    } on DioException {
      rethrow;
    }
  }

  /// PUT request with multipart/form-data and custom headers
  Future<dynamic> putMultipartWithHeaders(
    String path,
    Map<String, dynamic> jsonData, {
    Map<String, String>? headers,
  }) async {
    try {
      final accessToken = await storage.read(InstanceConstants.tokenKey);

      final formData = FormData.fromMap({
        '_content': jsonEncode(jsonData),
        '_content_type': 'application/json',
      });

      final Map<String, dynamic> allHeaders = {
        'Authorization': 'Bearer $accessToken',
        if (headers != null) ...headers,
      };

      final response = await dioClient.dio.put(
        '$baseUrl$path',
        data: formData,
        options: Options(
          headers: allHeaders,
          contentType: 'multipart/form-data',
        ),
      );
      return response.data;
    } on DioException {
      rethrow;
    }
  }

  /// PUT request with JWT authentication and custom headers
  Future<dynamic> putDataWithHeaders(
    String path,
    dynamic body, {
    Map<String, String>? headers,
  }) async {
    try {
      final accessToken = await storage.read(InstanceConstants.tokenKey);

      final Map<String, dynamic> allHeaders = {
        'Authorization': 'Bearer $accessToken',
        if (headers != null) ...headers,
      };

      final response = await dioClient.dio.put(
        '$baseUrl$path',
        data: body,
        options: Options(headers: allHeaders),
      );
      return response.data;
    } on DioException {
      rethrow;
    }
  }

  /// PUT request with JWT authentication from secure storage
  Future<dynamic> putData(String path, dynamic body) async {
    try {
      final accessToken = await storage.read(InstanceConstants.tokenKey);
      final response = await dioClient.dio.put(
        '$baseUrl$path',
        data: body,
        options: Options(headers: {
          'Authorization': 'Bearer $accessToken',
        }),
      );
      return response.data;
    } on DioException {
      rethrow;
    }
  }

  Future<dynamic> putFile(String path, String bodyName, XFile file) async {
    try {
      final accessToken = await storage.read(InstanceConstants.tokenKey);

      final extension = file_path.extension(file.path);
      String mimeType;
      switch (extension) {
        case '.png':
          mimeType = 'image/png';
          break;
        case '.jpg':
        case '.jpeg':
          mimeType = 'image/jpeg';
          break;
        case '.gif':
          mimeType = 'image/gif';
          break;
        default:
          throw Exception('Unsupported file type: $extension');
      }

      final multipartFile = await MultipartFile.fromFile(
        file.path,
        filename: file.name,
        contentType: DioMediaType.parse(mimeType),
      );

      final formData = FormData.fromMap({
        bodyName: multipartFile,
      });

      final response = await dioClient.dio.put(
        '$baseUrl$path',
        data: formData,
        options: Options(headers: {
          'Authorization': 'Bearer $accessToken',
          'Content-Type': 'multipart/form-data',
        }),
      );

      return response.data;
    } on DioException {
      rethrow;
    }
  }

  /// PATCH request with JWT authentication from secure storage
  Future<dynamic> patchData(String path, dynamic body) async {
    try {
      final accessToken = await storage.read(InstanceConstants.tokenKey);
      final response = await dioClient.dio.patch(
        '$baseUrl$path',
        data: body,
        options: Options(headers: {
          'Authorization': 'Bearer $accessToken',
        }),
      );
      return response.data;
    } on DioException {
      rethrow;
    }
  }

  /// DELETE request with JWT authentication from secure storage
  Future<dynamic> deleteData(String path, dynamic body) async {
    try {
      final accessToken = await storage.read(InstanceConstants.tokenKey);
      final response = await dioClient.dio.delete(
        '$baseUrl$path',
        data: body,
        options: Options(headers: {
          'Authorization': 'Bearer $accessToken',
        }),
      );
      return response.data;
    } on DioException {
      rethrow;
    }
  }

  /// Get data with custom cache TTL
  Future<dynamic> getDataWithCustomTtl(
    String path, {
    Map<String, dynamic>? params,
    int ttl = 3600,
  }) async {
    try {
      final response = await dioClient.dio.get(
        '$baseUrl$path',
        queryParameters: params,
        options: Options(
          extra: CacheUtils.withTtl({}, ttl),
        ),
      );

      return response.data;
    } on DioException {
      rethrow;
    }
  }

  Future<dynamic> postDataWithCache(String path, dynamic body, {int ttl = 3600}) async {
    try {
      final accessToken = await storage.read(InstanceConstants.tokenKey);
      final response = await dioClient.dio.post(
        '$baseUrl$path',
        data: body,
        options: Options(
          headers: {
            'Authorization': 'Bearer $accessToken',
          },
          extra: CacheUtils.withTtl(CacheUtils.forceCache({}), ttl),
        ),
      );

      return response.data;
    } on DioException {
      rethrow;
    }
  }

  // ============================================================================
  // UNIFIED CACHE MANAGEMENT METHODS
  // ============================================================================

  /// Clear all cache (both API and images)
  Future<void> clearAllCache() async {
    await cacheService.clearCache();
  }

  /// Alias for clearAllCache for backward compatibility
  Future<void> clearCache() async {
    await clearAllCache();
  }

  /// Clear only API cache
  Future<void> clearApiCache() async {
    await cacheService.clearApiCache();
  }

  /// Clear only image cache
  Future<void> clearImageCache() async {
    await cacheService.clearImageCache();
  }

  /// Clear cache for a specific API endpoint
  Future<void> clearCacheForEndpoint(String endpoint) async {
    await cacheService.clearEndpoint(endpoint);
  }

  /// Clear all site-specific cache entries for a given site ID
  /// This ensures data consistency when switching between sites
  Future<void> clearSiteSpecificCache(int siteId) async {
    await cacheService.clearSiteSpecificCache(siteId);
  }

  /// Get comprehensive cache statistics (API + Images)
  Future<Map<String, dynamic>> getCacheStats() async {
    return await cacheService.getCacheStats();
  }

  /// Remove a specific image from cache
  Future<void> removeImageFromCache(String url) async {
    await cacheService.removeImageFromCache(url);
  }

  /// Get all cache keys with detailed information
  Future<List<Map<String, dynamic>>> getAllCacheKeys() async {
    return await cacheService.getAllCacheKeys();
  }

  /// Get all cached image objects with detailed information
  Future<List<Map<String, dynamic>>> getAllImageCacheObjects() async {
    return await cacheService.getAllImageCacheObjects();
  }

  /// Reset cache metrics
  Future<void> resetCacheMetrics() async {
    await cacheService.resetMetrics();
  }
}
