import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/shared/widgets/draggable_modal_sheet.dart';

/// Utility functions for working with modals
class ModalUtils {
  /// Shows a draggable bottom sheet modal
  ///
  /// [context] - The build context
  /// [builder] - Builder function for modal content
  /// [isScrollControlled] - Whether the modal is scrollable
  /// [backgroundColor] - The background color of the modal
  /// [initialHeight] - Initial height of the modal (0.0 to 1.0)
  /// [useSafeArea] - Whether to respect safe area insets
  /// [barrierColor] - Color of the barrier behind the modal
  /// [radius] - Border radius for the top of the modal
  /// [isDismissible] - Whether the modal can be dismissed by tapping outside
  /// [transitionDuration] - Duration for the modal opening animation
  /// [reverseTransitionDuration] - Duration for the modal closing animation
  /// [snapToMid] - Whether the modal should snap to mid height when dragged
  /// [midHeight] - The mid height snap point (0.0 to 1.0)
  /// [swipeUpToExpand] - Whether the modal should expand when swiped up
  static Future<T?> showDraggableBottomSheet<T>({
    required BuildContext context,
    required Widget Function(BuildContext, double) builder,
    bool isScrollControlled = true,
    Color backgroundColor = Colors.white,
    double initialHeight = 1.0,
    bool useSafeArea = true,
    bool enableDrag = true,
    Color? barrierColor,
    double radius = 16.0,
    bool isDismissible = true,
    Duration transitionDuration = const Duration(milliseconds: 300),
    Duration reverseTransitionDuration = const Duration(milliseconds: 200),
    bool snapToMid = false,
    double midHeight = 0.5,
    bool swipeUpToExpand = false,
  }) {
    return showModalBottomSheet<T>(
      context: context,
      isScrollControlled: isScrollControlled,
      backgroundColor: Colors.transparent,
      barrierColor: barrierColor,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      useSafeArea: useSafeArea,
      transitionAnimationController: AnimationController(
        vsync: Navigator.of(context),
        duration: transitionDuration,
        reverseDuration: reverseTransitionDuration,
      ),
      builder: (context) {
        return ClipRRect(
          borderRadius: BorderRadius.vertical(top: Radius.circular(radius.r)),
          child: Material(
            color: backgroundColor,
            child: SafeArea(
              top: true,
              child: DraggableModalSheet(
                initialHeight: initialHeight,
                snapToMid: snapToMid,
                midHeight: midHeight,
                swipeUpToExpand: swipeUpToExpand,
                child: (modalHeight) => Scaffold(
                  backgroundColor: backgroundColor,
                  body: builder(context, modalHeight),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
