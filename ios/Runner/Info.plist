<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>Koc App</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleLocalizations</key>
		<array>
			<string>en</string>
			<string>vi</string>
		</array>
		<key>CFBundleName</key>
		<string>koc_app</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true/>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>CFBundleURLTypes</key>
		<array>
			<!-- Google Sign-in -->
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>REPLACE_WITH_GOOGLE_REVERSED_CLIENT_ID</string>
				</array>
			</dict>
			<!-- Facebook Sign-in -->
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>fbYOUR_FACEBOOK_APP_ID</string>
				</array>
			</dict>
		</array>
		<!-- Facebook Login configuration -->
		<key>FacebookAppID</key>
		<string>YOUR_FACEBOOK_APP_ID</string>
		<key>FacebookClientToken</key>
		<string>YOUR_FACEBOOK_CLIENT_TOKEN</string>
		<key>FacebookDisplayName</key>
		<string>KOC App</string>
		<key>FacebookAutoLogAppEventsEnabled</key>
		<string>TRUE</string>
		<key>FacebookAdvertiserIDCollectionEnabled</key>
		<string>TRUE</string>
		<!-- Additional Facebook configuration -->
		<key>NSUserTrackingUsageDescription</key>
		<string>This identifier will be used to deliver personalized ads to you.</string>
		<key>FacebookAppIDPrefix</key>
		<string>fb</string>
		<key>FacebookAppURLScheme</key>
		<string>fbYOUR_FACEBOOK_APP_ID</string>
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>googlechrome</string>
			<string>googlechromes</string>
			<string>fbapi</string>
			<string>fbapi20130214</string>
			<string>fbapi20130410</string>
			<string>fbapi20130702</string>
			<string>fbapi20131010</string>
			<string>fbapi20131219</string>
			<string>fbapi20140410</string>
			<string>fbapi20140116</string>
			<string>fbapi20150313</string>
			<string>fbapi20150629</string>
			<string>fbapi20160328</string>
			<string>fbauth</string>
			<string>fb-messenger-share-api</string>
			<string>fbauth2</string>
			<string>fbshareextension</string>
			<string>fbYOUR_FACEBOOK_APP_ID</string>
		</array>
		<key>UIStatusBarHidden</key>
		<false/>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false/>
		<key>NSMicrophoneUsageDescription</key>
		<string>This app requires access to the microphone for audio features.</string>
		<key>ITSAppUsesNonExemptEncryption</key>
		<false/>
	</dict>
</plist>
