# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:ios)

platform :ios do
  desc "Push a new beta build to TestFlight"
  lane :beta do
    setup_ci if ENV['CI']

    disable_automatic_code_signing(
      path: "Runner.xcodeproj",
      targets: ["Runner"]
    )

    match(
      type: "appstore",
      readonly: true,
      api_key: app_store_connect_api_key(
        key_id: ENV["APP_STORE_CONNECT_API_KEY_ID"],
        issuer_id: ENV["APP_STORE_CONNECT_ISSUER_ID"],
        key_content: ENV["APP_STORE_CONNECT_API_KEY"]
      )
    )

    update_code_signing_settings(
      use_automatic_signing: false,
      path: "Runner.xcodeproj",
      targets: ["Runner"],
      team_id: "N64VWWLFMZ",
      code_sign_identity: "Apple Distribution",
      profile_name: "match AppStore jp.ne.interspace.koc.app"
    )

    build_app(
      workspace: "Runner.xcworkspace",
      scheme: "Runner",
      export_xcargs: '-allowProvisioningUpdates',
      xcargs: '-allowProvisioningUpdates',
      export_team_id: 'N64VWWLFMZ',
      export_method: 'app-store'
    )

    upload_to_testflight(
      api_key: app_store_connect_api_key(
        key_id: ENV["APP_STORE_CONNECT_API_KEY_ID"],
        issuer_id: ENV["APP_STORE_CONNECT_ISSUER_ID"],
        key_content: ENV["APP_STORE_CONNECT_API_KEY"]
      )
    )
  end
end
