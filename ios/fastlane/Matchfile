storage_mode("s3")
s3_bucket("koc-app-fastlane-cert")

type("appstore") # The default type, can be: appstore, adhoc, enterprise or development

api_key_path("./fastlane/api_key.json")

app_identifier(["jp.ne.interspace.koc.app", "jp.ne.interspace.koc.app.staging"])
# username("<EMAIL>") # Your Apple Developer Portal username

# For all available options run `fastlane match --help`
# Remove the # in the beginning of the line to enable the other options

# The docs are available on https://docs.fastlane.tools/actions/match
